import { createClient } from '@supabase/supabase-js'
import { readFileSync } from 'fs'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// Supabase configuration
const supabaseUrl = 'https://nhgyywlfopodxomxbegx.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5oZ3l5d2xmb3BvZHhvbXhiZWd4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMTU1OTgsImV4cCI6MjA2Mjc5MTU5OH0.OaAqdBGba5YxutbQ8oIE_KULFGXdkBcfFzmdYUav4Sc'

const supabase = createClient(supabaseUrl, supabaseKey)

async function runMigration() {
  try {
    console.log('🚀 Running academic calendar documents migration...')
    
    // Read the migration file
    const migrationPath = join(__dirname, 'supabase_migrations', '20250111_create_academic_calendar_documents.sql')
    const migrationSQL = readFileSync(migrationPath, 'utf8')
    
    console.log('📄 Migration file loaded successfully')
    
    // Execute the migration
    const { data, error } = await supabase.rpc('exec_sql', { sql: migrationSQL })
    
    if (error) {
      console.error('❌ Migration failed:', error)
      return false
    }
    
    console.log('✅ Migration executed successfully!')
    
    // Test the table creation
    console.log('🔍 Testing table access...')
    const { data: testData, error: testError } = await supabase
      .from('academic_calendar_documents')
      .select('count', { count: 'exact', head: true })
    
    if (testError) {
      console.error('❌ Table test failed:', testError)
      return false
    }
    
    console.log('✅ Table is accessible and ready!')
    console.log('📊 Current document count:', testData)
    
    return true
    
  } catch (error) {
    console.error('💥 Unexpected error:', error)
    return false
  }
}

// Alternative method using direct SQL execution
async function runMigrationDirect() {
  try {
    console.log('🚀 Running academic calendar documents migration (direct method)...')
    
    // Create the table directly
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS academic_calendar_documents (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
        user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
        file_name TEXT NOT NULL,
        storage_file_path TEXT NOT NULL,
        file_mime_type TEXT NOT NULL,
        file_size_bytes BIGINT NOT NULL,
        CONSTRAINT academic_calendar_documents_file_size_check 
          CHECK (file_size_bytes > 0 AND file_size_bytes <= 10485760),
        CONSTRAINT academic_calendar_documents_file_name_check 
          CHECK (char_length(file_name) > 0 AND char_length(file_name) <= 255),
        CONSTRAINT academic_calendar_documents_storage_path_check 
          CHECK (char_length(storage_file_path) > 0),
        CONSTRAINT academic_calendar_documents_user_unique UNIQUE (user_id)
      );
    `
    
    console.log('📄 Creating table...')
    const { error: createError } = await supabase.rpc('exec_sql', { sql: createTableSQL })
    
    if (createError) {
      console.error('❌ Table creation failed:', createError)
      console.log('ℹ️  This might be because the exec_sql function is not available.')
      console.log('ℹ️  The table might need to be created manually in the Supabase dashboard.')
      return false
    }
    
    console.log('✅ Table created successfully!')
    
    // Test the table
    const { data: testData, error: testError } = await supabase
      .from('academic_calendar_documents')
      .select('count', { count: 'exact', head: true })
    
    if (testError) {
      console.error('❌ Table test failed:', testError)
      return false
    }
    
    console.log('✅ Table is accessible and ready!')
    return true
    
  } catch (error) {
    console.error('💥 Unexpected error:', error)
    return false
  }
}

// Test if table already exists
async function testTableExists() {
  try {
    console.log('🔍 Checking if academic_calendar_documents table exists...')
    
    const { data, error } = await supabase
      .from('academic_calendar_documents')
      .select('count', { count: 'exact', head: true })
    
    if (error) {
      if (error.code === '42P01') {
        console.log('❌ Table does not exist')
        return false
      } else {
        console.error('❌ Error checking table:', error)
        return false
      }
    }
    
    console.log('✅ Table exists and is accessible!')
    console.log('📊 Current document count:', data)
    return true
    
  } catch (error) {
    console.error('💥 Unexpected error:', error)
    return false
  }
}

// Main execution
async function main() {
  console.log('🎯 Academic Calendar Documents Migration Tool')
  console.log('=' .repeat(50))
  
  // First check if table exists
  const tableExists = await testTableExists()
  
  if (tableExists) {
    console.log('✅ Migration not needed - table already exists!')
    return
  }
  
  // Try to run migration
  console.log('📝 Table does not exist, attempting to create...')
  console.log('ℹ️  Note: This requires database admin privileges.')
  console.log('ℹ️  If this fails, please run the migration manually in Supabase dashboard.')
  console.log('')
  
  const success = await runMigrationDirect()
  
  if (!success) {
    console.log('')
    console.log('📋 Manual Migration Instructions:')
    console.log('1. Go to Supabase Dashboard > SQL Editor')
    console.log('2. Copy and paste the contents of:')
    console.log('   supabase_migrations/20250111_create_academic_calendar_documents.sql')
    console.log('3. Execute the SQL')
    console.log('4. Run this script again to verify')
  }
}

main().catch(console.error)
