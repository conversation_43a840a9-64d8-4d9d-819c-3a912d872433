import { navigateTo, defineNuxtRouteMiddleware } from "#app";
import { useSupabase } from "~/composables/useSupabase";

export default defineNuxtRouteMiddleware(async (to, from) => {
  const { client } = useSupabase();
  const {
    data: { session },
  } = await client.auth.getSession();

  const publicPaths = [
    "/auth/login",
    "/auth/daftar",
    "/auth/confirm",
    "/auth/butir-asas",    
  ]; // Added /auth/butir-asas for profile completion

  // If there is no session and the user is trying to access a protected page,
  // redirect them to the login page.
  if (!session && !publicPaths.includes(to.path)) {
    return navigateTo("/auth/login");
  }

  // If there IS a session and the user is trying to access a public auth page (like login or signup),
  // redirect them to the homepage (or a dashboard).
  // Exception for /auth/confirm which needs to be accessed with a session after OAuth redirect.
  if (session && (to.path === "/auth/login" || to.path === "/auth/daftar")) {
    return navigateTo("/");
  }

  // Otherwise, allow navigation to proceed.
});
