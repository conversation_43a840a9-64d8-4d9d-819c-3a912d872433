<template>
    <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
        <!-- Page Header -->
        <PageHeader title="Tetapan" subtitle="Urus keutamaan dan tetapan aplikasi anda" icon="heroicons:cog-6-tooth" />

        <!-- Main Content -->
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Navigation Tabs -->
            <div class="mb-8">
                <nav class="flex space-x-8" aria-label="Tabs">
                    <button @click="activeTab = 'templates'" :class="[
                        'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm',
                        activeTab === 'templates'
                            ? 'border-primary text-primary'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                    ]">
                        Template Refleksi
                    </button>
                    <button @click="activeTab = 'general'" :class="[
                        'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm',
                        activeTab === 'general'
                            ? 'border-primary text-primary'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                    ]">
                        Umum
                    </button>
                    <button @click="activeTab = 'notifications'" :class="[
                        'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm',
                        activeTab === 'notifications'
                            ? 'border-primary text-primary'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                    ]">
                        Notifikasi
                    </button>
                </nav>
            </div>

            <!-- Tab Content -->
            <div class="tab-content">
                <!-- Template Preferences Tab -->
                <div v-if="activeTab === 'templates'">
                    <TemplatePreferences />
                </div>

                <!-- General Preferences Tab -->
                <div v-if="activeTab === 'general'">
                    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                            Tetapan Umum
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400">
                            Tetapan umum akan ditambah di sini pada masa hadapan.
                        </p>
                    </div>
                </div>

                <!-- Notifications Tab -->
                <div v-if="activeTab === 'notifications'">
                    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                            Tetapan Notifikasi
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400">
                            Tetapan notifikasi akan ditambah di sini pada masa hadapan.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import PageHeader from '~/components/ui/composite/PageHeader.vue';
import TemplatePreferences from '~/components/preferences/TemplatePreferences.vue';

// Meta
definePageMeta({
    title: 'Tetapan',
    description: 'Urus keutamaan dan tetapan aplikasi anda',
    requiresAuth: true
});

// State
const activeTab = ref<'templates' | 'general' | 'notifications'>('templates');

// Head
useHead({
    title: 'Tetapan - EduPlan Pro',
    meta: [
        { name: 'description', content: 'Urus keutamaan dan tetapan aplikasi anda' }
    ]
});
</script>

<style scoped>
.tab-content {
    min-height: 400px;
}
</style>
