<template>
    <UiCompositeCard>
        <template #header>
            <div class="flex items-center space-x-2">
                <UiBaseIcon name="heroicons:document-plus-solid" class="w-5 h-5 text-primary" />
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Muat Naik RPH</h2>
            </div>
        </template>

        <div class="space-y-6">

            <!-- Class and Subject Selection -->
            <section>
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">1. <PERSON><PERSON><PERSON> dan Subjek</h2>
                <UiBaseMultiSelect id="class-subject-select" v-model="selectedClassSubjectsLocal"
                    :options="formattedUserClassSubjectsForSelect" placeholder="Pilih kelas dan subjek..."
                    :disabled="isLoadingParent || subjectsLoading" :loading="subjectsLoading" />
                <p v-if="selectedClassSubjectsDisplay"
                    class="mt-2 text-xs sm:text-sm text-gray-500 dark:text-gray-400 break-words">
                    Dipilih: {{ selectedClassSubjectsDisplay }}
                </p>
            </section>

            <!-- Day Selection -->
            <section>
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">2. Pilih Hari</h2>
                <div class="mb-2">
                    <label :class="[
                        'flex items-center space-x-2 p-2 border rounded-md cursor-pointer transition-colors',
                        selectAllDaysModel
                            ? 'bg-primary/5 border-primary/20 dark:bg-primary/10'
                            : 'hover:bg-primary/5 hover:border-primary/20 dark:hover:bg-primary/10'
                    ]">
                        <UiBaseCheckbox id="selectAllDaysRphForm" :modelValue="selectAllDaysModel"
                            @update:modelValue="toggleSelectAllDays" />
                        <span>Pilih Semua Hari</span>
                    </label>
                </div>
                <div class="grid grid-cols-2 sm:grid-cols-3 gap-2">
                    <label v-for="day in allDays" :key="day.id" :class="[
                        'flex items-center space-x-2 p-2 border rounded-md cursor-pointer transition-colors',
                        selectedDaysLocal.includes(day.id)
                            ? 'bg-primary/5 border-primary/20 dark:bg-primary/10'
                            : 'hover:bg-primary/5 hover:border-primary/20 dark:hover:bg-primary/10'
                    ]">
                        <UiBaseCheckbox :id="`day-rph-form-${day.id}`" :modelValue="selectedDaysLocal.includes(day.id)"
                            @update:modelValue="(isChecked: boolean) => updateSelectedDays(day.id, isChecked)" />
                        <span>{{ day.name }}</span>
                    </label>
                </div>
            </section> <!-- File Upload -->
            <section>
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    3. {{ editingPlanId ? 'Ganti' : 'Muat Naik' }} Dokumen RPH
                </h2>
                <FileUpload id="rph-file-upload" v-model="uploadedFileLocal" :max-size-mb="maxFileSizeMb"
                    :is-editing="!!editingPlanId"
                    :existing-file-name="editingPlanId && currentEditingPlanData ? currentEditingPlanData.file_name : null"
                    @file-error="handleFileErrorInternal" :disabled="isLoadingParent" />
                <p v-if="fileUploadErrorLocal"
                    class="mt-2 text-xs sm:text-sm text-red-600 dark:text-red-400 break-words">{{
                        fileUploadErrorLocal
                    }}</p>
            </section>

            <!-- Submit Button for individual lesson plan -->
            <section
                :class="['mt-8 flex flex-col sm:flex-row sm:items-center gap-4', editingPlanId ? 'sm:justify-between' : 'sm:justify-end']">
                <UiBaseButton v-if="editingPlanId" @click="handleCancelInternal" variant="outline" :size="buttonSize"
                    :disabled="isLoadingParent" class="w-full sm:w-auto">
                    Batal Kemaskini
                </UiBaseButton>
                <UiBaseButton @click="editingPlanId ? handleUpdateInternal() : handleSubmitInternal()" variant="primary"
                    :size="buttonSize" :disabled="!canSubmitOrUpdateInternal || isFormSubmitting"
                    class="w-full sm:w-auto">
                    <span v-if="isFormSubmitting">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline"
                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                            </circle>
                            <path class="opacity-75" fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                            </path>
                        </svg>
                        Memproses...
                    </span>
                    <span v-else class="break-words">
                        {{ editingPlanId ? 'Kemaskini RPH' : 'Simpan RPH' }}
                    </span>
                </UiBaseButton>
            </section>
        </div>
    </UiCompositeCard>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import type { PropType } from 'vue';
import type { LessonPlan, DayOption as GlobalDayOption } from '~/types/lessonPlans';
import type { UserClassSubjectEntry } from '~/schemas/userSchemas';
import { useSubjects } from '@/composables/useSubjects';
// useTeacherSchedules no longer needed for validation
import { useTeacherScheduleHelpers } from '~/composables/useTeacherScheduleHelpers';
import { useTimetable } from '~/composables/useTimetable';
import type { RphWeek } from '~/types/rph';
import type { DayOfWeek, TeacherScheduleValidationErrors } from '~/types/teacherSchedule';
import FileUpload from '~/components/rph/FileUpload.vue';
import UiBaseMultiSelect from '~/components/ui/base/MultiSelect.vue';
import UiBaseCheckbox from '~/components/ui/base/Checkbox.vue';
import UiBaseButton from '~/components/ui/base/Button.vue';
import UiBaseIcon from '~/components/ui/base/Icon.vue';
import UiCompositeCard from '~/components/ui/composite/Card.vue';

// Define Props
const props = defineProps({
    editingPlanId: {
        type: String as PropType<string | null>,
        default: null,
    },
    currentEditingPlanData: {
        type: Object as PropType<LessonPlan | null>,
        default: null,
    },
    userClassSubjects: {
        type: Array as PropType<UserClassSubjectEntry[]>, // Use UserClassSubjectEntry
        required: true,
    },
    allDays: {
        type: Array as PropType<GlobalDayOption[]>, // Corrected prop type
        required: true,
    },
    currentSelectedWeek: {
        type: Object as PropType<RphWeek | null>, // Corrected prop type
        required: true,
    },
    isLoadingParent: {
        type: Boolean,
        default: false,
    },
    maxFileSizeMb: {
        type: Number, // Corrected prop type
        default: 10,
    },
});

// Define Emits
const emit = defineEmits([
    'submit-rph',
    'update-rph',
    'cancel-edit',
    'file-error', // For file validation errors from FileUpload component
    'form-alert'  // For general form validation messages to be displayed by parent
]);

// Integrate useSubjects
const { subjects, loading: subjectsLoading, error: subjectsError } = useSubjects();
// Teacher schedules composable no longer needed for validation

const localClassSubjects = computed(() => {
    const { getAvailableClassSubjectsFromTimetable } = useTeacherScheduleHelpers();
    const { timetableEntries } = useTimetable();

    if (subjectsLoading.value) {
        return [{ value: '', label: 'Memuatkan senarai subjek...', disabled: true }];
    }
    if (subjectsError.value) {
        return [{ value: '', label: 'Ralat memuatkan subjek.', disabled: true }];
    }

    // Get available class-subjects from timetable entries
    const availableFromTimetable = getAvailableClassSubjectsFromTimetable(timetableEntries.value);

    if (availableFromTimetable.length === 0) {
        return [{ value: '', label: 'Tiada jadual waktu ditemui. Sila lengkapkan jadual waktu anda.', disabled: true }];
    }

    return availableFromTimetable.map(entry => {
        const compositeId = `${entry.class_id}_${entry.subject_id}`;
        return {
            value: compositeId,
            label: entry.combined_label
        };
    });
});

// Helper to get subject name by ID
const getSubjectNameById = (subjectId: string | number | null): string => {
    if (subjectId === null || subjectId === undefined) return 'Subjek Tidak Sah';
    // Ensure we are comparing string to string, as subject.id is now a UUID string
    const idToFind: string = String(subjectId);
    // if (isNaN(idToFind)) return `ID Subjek Tidak Sah: ${subjectId}`; // This check is for numbers, not UUIDs
    const subject = subjects.value.find(s => s.id === idToFind);
    return subject ? subject.name : `Subjek (ID: ${idToFind}) Tidak Dijumpai`;
};

// Computed property for MultiSelect options
const formattedUserClassSubjectsForSelect = computed(() => {
    if (subjectsLoading.value && !subjects.value.length) {
        return [{ value: '', label: 'Memuatkan senarai subjek...', disabled: true }];
    }
    if (subjectsError.value) {
        return [{ value: '', label: 'Ralat memuatkan subjek.', disabled: true }];
    }
    return props.userClassSubjects.map(entry => {
        const subjectName = getSubjectNameById(entry.subject_id);
        // Create a composite key for the value
        const compositeId = `${entry.class_id}_${entry.subject_id}`;
        return {
            value: compositeId,
            label: `${entry.className} - ${subjectName}`
        };
    });
});

// Internal State for form fields
const selectedClassSubjectsLocal = ref<string[]>([]); // Stores composite IDs: "classId_subjectId"
const selectedDaysLocal = ref<string[]>([]);
const selectAllDaysModel = ref<boolean>(false);
const uploadedFileLocal = ref<File | null>(null);
const fileUploadErrorLocal = ref<string | null>(null); // Error from FileUpload component
const isFormSubmitting = ref<boolean>(false); // Track form submission state separately

// Computed Properties
const selectedClassSubjectsDisplay = computed(() => {
    if (subjectsLoading.value && !selectedClassSubjectsLocal.value.length) return 'Memuatkan...';
    return selectedClassSubjectsLocal.value
        .map(compositeId => {
            const selectedOption = formattedUserClassSubjectsForSelect.value.find(opt => opt.value === compositeId);
            return selectedOption ? selectedOption.label : `ID: ${compositeId}`;
        })
        .join(', ');
});

const selectedDaysDisplay = computed(() => {
    return selectedDaysLocal.value
        .map(dayId => {
            const dayObj = props.allDays.find(d => d.id === dayId);
            return dayObj ? dayObj.name : dayId;
        })
        .join(', ');
});

const MAX_FILE_SIZE_BYTES = computed(() => (props.maxFileSizeMb || 10) * 1024 * 1024);

const canSubmitOrUpdateInternal = computed(() => {
    const isFileValid = props.editingPlanId || uploadedFileLocal.value;
    const isFileSizeOk = uploadedFileLocal.value ? uploadedFileLocal.value.size <= MAX_FILE_SIZE_BYTES.value : true;
    const hasSelectedClassSubjects = selectedClassSubjectsLocal.value.length > 0;
    const hasSelectedDays = selectedDaysLocal.value.length > 0;

    return !!(props.currentSelectedWeek && // Must have a week selected
        hasSelectedClassSubjects &&       // Must have at least one class-subject selected
        hasSelectedDays &&                // Must have at least one day selected
        isFileValid &&                    // Must have a file if not editing
        isFileSizeOk &&                   // File size must be okay
        !fileUploadErrorLocal.value);     // No current file upload error
});

const buttonSize = computed((): "sm" | "md" | "lg" => {
    // Use medium size for better mobile experience
    return 'md';
});

// Methods
const updateSelectedDays = (dayId: string, isChecked: boolean) => {
    if (isChecked) {
        if (!selectedDaysLocal.value.includes(dayId)) {
            selectedDaysLocal.value.push(dayId);
        }
    } else {
        selectedDaysLocal.value = selectedDaysLocal.value.filter(d => d !== dayId);
    }
};

const toggleSelectAllDays = (isChecked: boolean) => {
    selectAllDaysModel.value = isChecked;
    if (isChecked) {
        selectedDaysLocal.value = props.allDays.map(day => day.id);
    } else {
        selectedDaysLocal.value = [];
    }
};

const handleFileErrorInternal = (errorMessage: string | null) => {
    fileUploadErrorLocal.value = errorMessage;
    emit('file-error', errorMessage);
};

const resetFormInternal = () => {
    selectedClassSubjectsLocal.value = [];
    selectedDaysLocal.value = [];
    uploadedFileLocal.value = null;
    fileUploadErrorLocal.value = null;
    selectAllDaysModel.value = false;
    isFormSubmitting.value = false; // Clear form submission state
    // FileUpload component's input is reset via its own internal logic when v-model (uploadedFileLocal) becomes null
};

// Helper to get subject name by ID (already exists)
// Helper to get class name by ID (add if needed, or use existing from useTeacherSchedules)

// Validate timetable data using new timetable-based approach
const validateTimetable = (): TeacherScheduleValidationErrors | null => {
    const { validateTimetableCompatibility } = useTeacherScheduleHelpers();
    const { timetableEntries } = useTimetable();

    const validation = validateTimetableCompatibility(
        timetableEntries.value,
        selectedClassSubjectsLocal.value,
        selectedDaysLocal.value,
        props.userClassSubjects // Pass userClassSubjects for better name resolution
    );

    if (!validation.isValid) {
        return {
            general: validation.errors
        };
    }

    return null;
};

const handleSubmitInternal = () => {
    // Clear previous alerts
    emit('form-alert', null);

    if (!canSubmitOrUpdateInternal.value) {
        emit('form-alert', { type: 'error', message: 'Sila lengkapkan semua medan yang diperlukan dan pastikan fail adalah sah.' });
        return;
    }
    if (!props.currentSelectedWeek) { // Should be caught by canSubmitOrUpdate, but as a safeguard
        emit('form-alert', { type: 'error', message: 'Minggu tidak dipilih.' });
        return;
    }
    if (!uploadedFileLocal.value) { // Essential for new submissions
        emit('form-alert', { type: 'error', message: 'Sila muat naik fail RPH.' });
        return;
    }

    // Validate timetable
    const timetableErrors = validateTimetable();
    if (timetableErrors) {
        const errorMessages = timetableErrors.general?.join(', ') || 'Ralat pengesahan jadual waktu.';
        emit('form-alert', { type: 'error', message: errorMessages });
        return;
    }

    isFormSubmitting.value = true; // Set form submission state
    const planDetails = {
        class_subject_ids: selectedClassSubjectsLocal.value, // Pass composite IDs
        days_selected: selectedDaysLocal.value as DayOfWeek[], // Cast to DayOfWeek[]
    };
    emit('submit-rph', { planDetails, file: uploadedFileLocal.value });
    // resetFormInternal(); // Reset form after successful submission signal from parent
};

const handleUpdateInternal = () => {
    // Clear previous alerts
    emit('form-alert', null);

    if (!canSubmitOrUpdateInternal.value) {
        emit('form-alert', { type: 'error', message: 'Sila lengkapkan semua medan yang diperlukan dan pastikan fail adalah sah (jika diganti).' });
        return;
    }
    if (!props.editingPlanId || !props.currentEditingPlanData || !props.currentSelectedWeek) {
        emit('form-alert', { type: 'error', message: 'Data tidak lengkap untuk kemaskini.' });
        return;
    }

    // Validate timetable
    const timetableErrors = validateTimetable();
    if (timetableErrors) {
        const errorMessages = timetableErrors.general?.join(', ') || 'Ralat pengesahan jadual waktu.';
        emit('form-alert', { type: 'error', message: errorMessages });
        return;
    }

    isFormSubmitting.value = true; // Set form submission state
    const planUpdateDetails = {
        class_subject_ids: selectedClassSubjectsLocal.value, // Pass composite IDs
        days_selected: selectedDaysLocal.value as DayOfWeek[], // Cast to DayOfWeek[]
    };
    emit('update-rph', {
        planId: props.editingPlanId!,
        originalFilePath: props.currentEditingPlanData!.storage_file_path,
        planUpdateDetails,
        file: uploadedFileLocal.value // Can be null if file is not changed
    });
};

const handleCancelInternal = () => {
    emit('cancel-edit');
    // Parent will call resetForm on this component by clearing editingPlanId prop,
    // which will trigger the watcher below.
};

// Watchers
watch(selectedDaysLocal, (newSelectedDays) => {
    selectAllDaysModel.value = newSelectedDays.length === props.allDays.length && props.allDays.length > 0;
}, { deep: true });

watch(() => props.allDays, (newAllDays) => {
    selectAllDaysModel.value = selectedDaysLocal.value.length === newAllDays.length && newAllDays.length > 0;
}, { deep: true });

watch(() => props.editingPlanId, (newPlanId) => {
    if (newPlanId && props.currentEditingPlanData) {
        // Populate selectedClassSubjectsLocal with composite IDs
        selectedClassSubjectsLocal.value = props.currentEditingPlanData.class_subject_ids.map(csId => {
            // Assuming csId is already the composite ID from the database or needs to be reconstructed
            // If csId from plan.class_subject_ids is NOT composite, we need to find the matching UserClassSubjectEntry
            // and construct the composite ID. For now, let's assume it IS the composite ID.
            // This part might need adjustment based on how class_subject_ids are stored in LessonPlan
            return csId;
        });
        selectedDaysLocal.value = [...props.currentEditingPlanData.days_selected];
        uploadedFileLocal.value = null; // Clear previous file, user must re-upload if changing
        fileUploadErrorLocal.value = null;
        selectAllDaysModel.value = selectedDaysLocal.value.length === props.allDays.length;

    } else {
        resetFormInternal();
    }
}, { immediate: true });

watch(() => props.currentEditingPlanData, (newPlanData) => {
    if (props.editingPlanId && newPlanData) {
        selectedClassSubjectsLocal.value = newPlanData.class_subject_ids.map(id => id);
        selectedDaysLocal.value = [...newPlanData.days_selected];
        // uploadedFileLocal is not set here to allow user to choose a new file or keep existing
        selectAllDaysModel.value = selectedDaysLocal.value.length === props.allDays.length && props.allDays.length > 0;
    }
}, { deep: true, immediate: true }); // immediate: true to populate form on initial load if editing

const clearFormSubmissionState = () => {
    isFormSubmitting.value = false;
};

defineExpose({ resetFormInternal, clearFormSubmissionState });

</script>

<style scoped>
/* Add any specific styles for RphForm.vue if needed */
</style>
