<template>
    <div class="space-y-4">
        <!-- Results Count -->
        <div v-if="events.length > 0 || totalCount > 0" class="text-sm text-gray-600 dark:text-gray-400">
            Menunjukkan {{ events.length }} daripada {{ totalCount }} keputusan
        </div>

        <!-- No Results -->
        <div v-if="events.length === 0 && !loading" class="text-center py-12">
            <UiBaseIcon name="heroicons:magnifying-glass-solid" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Tiada Keputusan</h3>
            <p class="text-gray-500 dark:text-gray-400">
                Tiada acara dijumpai berdasarkan carian dan tapis yang dipilih.
            </p>
        </div>

        <!-- Search Results -->
        <div v-else class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            <div v-for="event in events" :key="event.id"
                class="bg-white dark:bg-dark-card rounded-lg shadow-sm border border-gray-200 dark:border-dark-border hover:shadow-md transition-shadow cursor-pointer overflow-hidden"
                :class="`border-l-4 border-l-${getCategoryConfig(event.category).color}`"
                @click="handleEventClick(event)">

                <!-- Event Header -->
                <div class="p-4">
                    <div class="flex items-start justify-between mb-2">
                        <h3 class="font-semibold text-gray-900 dark:text-white text-sm line-clamp-2 flex-1">
                            {{ event.title }}
                        </h3>
                        <div class="ml-2 flex-shrink-0">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                                :class="`${getCategoryConfig(event.category).bgColor} ${getCategoryConfig(event.category).textColor}`">
                                {{ getCategoryConfig(event.category).label }}
                            </span>
                        </div>
                    </div>

                    <!-- Event Details -->
                    <div class="space-y-1 text-xs text-gray-600 dark:text-gray-400">
                        <!-- Date -->
                        <div class="flex items-center space-x-1">
                            <UiBaseIcon name="heroicons:calendar-days-solid" class="w-3 h-3 flex-shrink-0" />
                            <span>{{ formatEventDate(event) }}</span>
                        </div>

                        <!-- Time -->
                        <div v-if="!event.is_all_day && event.start_time" class="flex items-center space-x-1">
                            <UiBaseIcon name="heroicons:clock-solid" class="w-3 h-3 flex-shrink-0" />
                            <span>{{ formatEventTime(event) }}</span>
                        </div>

                        <!-- Location -->
                        <div v-if="event.location" class="flex items-center space-x-1">
                            <UiBaseIcon name="heroicons:map-pin-solid" class="w-3 h-3 flex-shrink-0" />
                            <span class="truncate">{{ event.location }}</span>
                        </div>
                    </div>

                    <!-- Description -->
                    <p v-if="event.description" class="mt-2 text-xs text-gray-500 dark:text-gray-400 line-clamp-2">
                        {{ event.description }}
                    </p>
                </div>
            </div>
        </div>

        <!-- Loading More -->
        <div v-if="loading" class="flex items-center justify-center py-8">
            <UiBaseIcon name="mdi:loading" class="w-6 h-6 animate-spin text-primary mr-2" />
            <span class="text-gray-600 dark:text-gray-400">Memuatkan lagi...</span>
        </div>

        <!-- Load More Trigger (invisible, for intersection observer) -->
        <div ref="loadMoreTrigger" class="h-4"></div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import type { CalendarEvent } from '~/types/calendar'
import { getCategoryConfig } from '~/types/calendar'

// Props
interface Props {
    events: readonly CalendarEvent[]
    totalCount: number
    loading: boolean
    hasMore: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
    'event-click': [event: CalendarEvent]
    'load-more': []
}>()

// Refs
const loadMoreTrigger = ref<HTMLElement>()
let observer: IntersectionObserver | null = null

// Methods
const handleEventClick = (event: CalendarEvent) => {
    emit('event-click', event)
}

const formatEventDate = (event: CalendarEvent) => {
    const startDate = new Date(event.start_date)
    const endDate = event.end_date ? new Date(event.end_date) : null

    if (endDate && endDate.getTime() !== startDate.getTime()) {
        return `${startDate.toLocaleDateString('ms-MY')} - ${endDate.toLocaleDateString('ms-MY')}`
    }
    return startDate.toLocaleDateString('ms-MY')
}

const formatEventTime = (event: CalendarEvent) => {
    if (event.is_all_day) {
        return 'Sepanjang hari'
    }

    if (event.start_time) {
        const endTime = event.end_time ? ` - ${event.end_time}` : ''
        return `${event.start_time}${endTime}`
    }

    return 'Tiada masa ditetapkan'
}

// Intersection Observer for infinite scroll
onMounted(() => {
    if (loadMoreTrigger.value) {
        observer = new IntersectionObserver(
            (entries) => {
                const entry = entries[0]
                if (entry.isIntersecting && props.hasMore && !props.loading) {
                    emit('load-more')
                }
            },
            {
                rootMargin: '100px'
            }
        )
        observer.observe(loadMoreTrigger.value)
    }
})

onUnmounted(() => {
    if (observer) {
        observer.disconnect()
    }
})
</script>

<style scoped>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
