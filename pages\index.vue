<template>
  <!-- Hero Section -->
  <div
    class="min-h-screen bg-gradient-to-br from-primary/5 via-light-background to-accent/5 dark:from-primary/10 dark:via-dark-background dark:to-accent/10 relative overflow-hidden">
    <!-- Background Decorative Elements -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <!-- Animated Circles -->
      <div class="absolute -top-20 -right-20 w-80 h-80 bg-primary/10 rounded-full animate-pulse"></div>
      <div class="absolute -bottom-20 -left-20 w-96 h-96 bg-accent/5 rounded-full animate-pulse delay-1000"></div>
      <div class="absolute top-1/3 left-1/4 w-32 h-32 bg-secondary/5 rounded-full animate-pulse delay-500"></div>
    </div>

    <!-- Main Content Container -->
    <div class="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Navigation Breadcrumb -->
      <nav class="pt-8 pb-4">
        <div class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
          <UiBaseIcon name="heroicons:home-solid" class="w-4 h-4" />
          <span>Dashboard</span>
        </div>
      </nav>

      <!-- Hero Content -->
      <div class="flex flex-col lg:flex-row items-center justify-between py-16 lg:py-24 gap-12">

        <!-- Left Side - Content -->
        <div class="flex-1 text-center lg:text-left space-y-8 max-w-2xl">
          <!-- Main Heading -->
          <div class="space-y-4">
            <div
              class="inline-flex items-center px-4 py-2 bg-primary/10 dark:bg-primary/20 rounded-full text-primary dark:text-primary text-sm font-medium mb-4">
              <UiBaseIcon name="heroicons:sparkles-solid" class="w-4 h-4 mr-2" />
              Welcome to EduPlan Pro
            </div>
            <h1
              class="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white leading-tight hero-title">
              Streamline Your
              <span class="text-transparent bg-clip-text bg-gradient-to-r from-primary to-accent gradient-text">
                Teaching Journey
              </span>
            </h1>
            <p
              class="text-lg sm:text-xl text-gray-600 dark:text-gray-300 leading-relaxed max-w-xl mx-auto lg:mx-0 hero-description">
              A comprehensive educational platform built with modern technologies. Manage your classes, track progress,
              and enhance your teaching experience.
            </p>
          </div>

          <!-- Feature Highlights -->
          <div class="grid grid-cols-1 sm:grid-cols-3 gap-6 py-8">
            <div class="text-center lg:text-left">
              <div
                class="w-12 h-12 bg-primary/10 dark:bg-primary/20 rounded-lg flex items-center justify-center mx-auto lg:mx-0 mb-3">
                <UiBaseIcon name="heroicons:academic-cap-solid" class="w-6 h-6 text-primary" />
              </div>
              <h3 class="font-semibold text-gray-900 dark:text-white mb-1">Smart Planning</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">Organize classes and subjects efficiently</p>
            </div>
            <div class="text-center lg:text-left">
              <div
                class="w-12 h-12 bg-accent/10 dark:bg-accent/20 rounded-lg flex items-center justify-center mx-auto lg:mx-0 mb-3">
                <UiBaseIcon name="heroicons:chart-bar-solid" class="w-6 h-6 text-accent" />
              </div>
              <h3 class="font-semibold text-gray-900 dark:text-white mb-1">Progress Tracking</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">Monitor student development</p>
            </div>
            <div class="text-center lg:text-left">
              <div
                class="w-12 h-12 bg-secondary/10 dark:bg-secondary/20 rounded-lg flex items-center justify-center mx-auto lg:mx-0 mb-3">
                <UiBaseIcon name="heroicons:users-solid" class="w-6 h-6 text-secondary" />
              </div>
              <h3 class="font-semibold text-gray-900 dark:text-white mb-1">Collaboration</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">Connect with other educators</p>
            </div>
          </div> <!-- Action Buttons -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
            <NuxtLink to="/items">
              <UiBaseButton variant="primary" size="lg" prependIcon="heroicons:arrow-right-solid"
                class="w-full sm:w-auto">
                Explore Features
              </UiBaseButton>
            </NuxtLink>
            <NuxtLink to="/components">
              <UiBaseButton variant="outline" size="lg" prependIcon="heroicons:eye-solid" class="w-full sm:w-auto">
                View Components
              </UiBaseButton>
            </NuxtLink>
          </div>

          <!-- Tech Stack Tags -->
          <div class="pt-8">
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-4">Built with modern technologies:</p>
            <div class="flex flex-wrap gap-3 justify-center lg:justify-start">
              <span
                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                <UiBaseIcon name="logos:nuxt-icon" class="w-3 h-3 mr-1" />
                Nuxt 3
              </span>
              <span
                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                <UiBaseIcon name="logos:typescript-icon" class="w-3 h-3 mr-1" />
                TypeScript
              </span>
              <span
                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-cyan-100 text-cyan-800 dark:bg-cyan-900/30 dark:text-cyan-300">
                <UiBaseIcon name="logos:tailwindcss-icon" class="w-3 h-3 mr-1" />
                Tailwind CSS
              </span>
              <span
                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-300">
                <UiBaseIcon name="logos:supabase-icon" class="w-3 h-3 mr-1" />
                Supabase
              </span>
            </div>
          </div>
        </div>

        <!-- Right Side - Interactive Visual -->
        <div class="flex-1 max-w-lg w-full">
          <div class="relative">
            <!-- Main Card -->
            <UiCompositeCard
              class="backdrop-blur-sm bg-white/80 dark:bg-dark-card/80 border border-white/20 dark:border-dark-border/20">
              <template #header>
                <div class="flex items-center space-x-3">
                  <div
                    class="w-10 h-10 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center">
                    <UiBaseIcon name="heroicons:chart-pie-solid" class="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Dashboard Preview</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Real-time insights</p>
                  </div>
                </div>
              </template>

              <!-- Demo Content -->
              <div class="space-y-6">
                <!-- Stats Grid -->
                <div class="grid grid-cols-2 gap-4">
                  <div class="text-center p-4 bg-primary/5 dark:bg-primary/10 rounded-lg">
                    <div class="text-2xl font-bold text-primary mb-1">24</div>
                    <div class="text-xs text-gray-600 dark:text-gray-400">Active Classes</div>
                  </div>
                  <div class="text-center p-4 bg-accent/5 dark:bg-accent/10 rounded-lg">
                    <div class="text-2xl font-bold text-accent mb-1">156</div>
                    <div class="text-xs text-gray-600 dark:text-gray-400">Students</div>
                  </div>
                </div>

                <!-- Progress Bars -->
                <div class="space-y-3">
                  <div>
                    <div class="flex justify-between text-sm mb-1">
                      <span class="text-gray-600 dark:text-gray-400">Weekly Progress</span>
                      <span class="text-primary font-medium">85%</span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div class="bg-gradient-to-r from-primary to-accent h-2 rounded-full animate-pulse"
                        style="width: 85%"></div>
                    </div>
                  </div>
                  <div>
                    <div class="flex justify-between text-sm mb-1">
                      <span class="text-gray-600 dark:text-gray-400">Monthly Goals</span>
                      <span class="text-accent font-medium">92%</span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div class="bg-gradient-to-r from-accent to-secondary h-2 rounded-full animate-pulse delay-300"
                        style="width: 92%"></div>
                    </div>
                  </div>
                </div>

                <!-- Recent Activity -->
                <div>
                  <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Recent Activity</h4>
                  <div class="space-y-2">
                    <div
                      class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
                      <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span class="text-sm text-gray-600 dark:text-gray-400">Mathematics class completed</span>
                    </div>
                    <div
                      class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
                      <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse delay-150"></div>
                      <span class="text-sm text-gray-600 dark:text-gray-400">New student enrolled</span>
                    </div>
                    <div
                      class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
                      <div class="w-2 h-2 bg-yellow-500 rounded-full animate-pulse delay-300"></div>
                      <span class="text-sm text-gray-600 dark:text-gray-400">Assignment graded</span>
                    </div>
                  </div>
                </div>
              </div>
            </UiCompositeCard>

            <!-- Floating Elements -->
            <div
              class="absolute -top-4 -right-4 w-16 h-16 bg-gradient-to-br from-primary/20 to-accent/20 rounded-xl rotate-12 animate-pulse">
            </div>
            <div
              class="absolute -bottom-4 -left-4 w-12 h-12 bg-gradient-to-br from-accent/20 to-secondary/20 rounded-lg -rotate-12 animate-pulse delay-500">
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions Section -->
      <div class="pb-16">
        <UiCompositeCard
          class="backdrop-blur-sm bg-white/50 dark:bg-dark-card/50 border border-white/20 dark:border-dark-border/20">
          <template #header>
            <div class="text-center">
              <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">Quick Actions</h2>
              <p class="text-gray-600 dark:text-gray-400">Get started with these common tasks</p>
            </div>
          </template>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"> <!-- Action Card 1 -->
            <NuxtLink to="/items" class="group">
              <div
                class="action-card p-6 rounded-xl bg-gradient-to-br from-primary/5 to-primary/10 dark:from-primary/10 dark:to-primary/20 border border-primary/20 hover:border-primary/40 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
                <div
                  class="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                  <UiBaseIcon name="heroicons:rectangle-stack-solid" class="w-6 h-6 text-primary" />
                </div>
                <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Manage Items</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">View and organize your educational resources</p>
              </div>
            </NuxtLink>

            <!-- Action Card 2 -->
            <NuxtLink to="/components" class="group">
              <div
                class="action-card p-6 rounded-xl bg-gradient-to-br from-accent/5 to-accent/10 dark:from-accent/10 dark:to-accent/20 border border-accent/20 hover:border-accent/40 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
                <div
                  class="w-12 h-12 bg-accent/20 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                  <UiBaseIcon name="heroicons:puzzle-piece-solid" class="w-6 h-6 text-accent" />
                </div>
                <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Components</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">Explore UI components and interactions</p>
              </div>
            </NuxtLink>

            <!-- Action Card 3 -->
            <NuxtLink to="/ui" class="group">
              <div
                class="action-card p-6 rounded-xl bg-gradient-to-br from-secondary/5 to-secondary/10 dark:from-secondary/10 dark:to-secondary/20 border border-secondary/20 hover:border-secondary/40 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
                <div
                  class="w-12 h-12 bg-secondary/20 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                  <UiBaseIcon name="heroicons:paint-brush-solid" class="w-6 h-6 text-secondary" />
                </div>
                <h3 class="font-semibold text-gray-900 dark:text-white mb-2">UI Elements</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">Browse available UI elements and controls</p>
              </div>
            </NuxtLink>

            <!-- Action Card 4 -->
            <div class="group cursor-pointer">
              <div
                class="action-card p-6 rounded-xl bg-gradient-to-br from-emerald-500/5 to-emerald-500/10 dark:from-emerald-500/10 dark:to-emerald-500/20 border border-emerald-500/20 hover:border-emerald-500/40 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
                <div
                  class="w-12 h-12 bg-emerald-500/20 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                  <UiBaseIcon name="heroicons:plus-solid" class="w-6 h-6 text-emerald-500" />
                </div>
                <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Create New</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">Start a new project or class setup</p>
              </div>
            </div>
          </div>
        </UiCompositeCard>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Define page meta
definePageMeta({
  layout: 'default',
  // middleware: 'auth' is global, so it's already applied
});

// Set page head
useHead({
  title: 'EduPlan Pro - Streamline Your Teaching Journey',
  meta: [
    {
      name: 'description',
      content: 'A comprehensive educational platform built with Nuxt 3, TypeScript, Tailwind CSS, and Supabase. Manage your classes, track progress, and enhance your teaching experience.'
    },
    {
      property: 'og:title',
      content: 'EduPlan Pro - Streamline Your Teaching Journey'
    },
    {
      property: 'og:description',
      content: 'A comprehensive educational platform built with modern technologies.'
    }
  ]
});
</script>

<style scoped>
/* Custom animations for the hero section */
@keyframes float-gentle {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-gradient {

  0%,
  100% {
    opacity: 0.5;
  }

  50% {
    opacity: 0.8;
  }
}

/* Ensure smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom gradient text animation */
.gradient-text {
  background: linear-gradient(-45deg, #0085DB, #F59E0B, #0085DB, #F59E0B);
  background-size: 400% 400%;
  animation: gradient-shift 3s ease infinite;
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

/* Enhance hover effects */
.action-card {
  transition: all 0.3s ease;
}

.action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Mobile-specific improvements */
@media (max-width: 640px) {
  .hero-title {
    font-size: 2.5rem;
    line-height: 1.1;
  }

  .hero-description {
    font-size: 1rem;
  }
}

/* Dark mode specific adjustments */
@media (prefers-color-scheme: dark) {
  .glass-card {
    backdrop-filter: blur(16px);
    background: rgba(17, 28, 45, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}
</style>
