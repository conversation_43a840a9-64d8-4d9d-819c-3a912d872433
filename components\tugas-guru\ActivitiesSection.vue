<template>
  <div class="space-y-6">
    <!-- Sukan Section -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <!-- Category Header -->
      <div
        class="flex items-center justify-center border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-800">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Sukan</h3>
      </div>

      <!-- Activities List -->
      <div class="space-y-2 col-span-2">
        <!-- Add Activity Form -->
        <div v-if="showAddForms.sukan"
          class="p-3 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800">
          <div class="flex items-center space-x-3">
            <Input v-model="newActivityDescriptions.sukan" placeholder="Taipkan Aktiviti / Kegiatan" class="flex-1"
              variant="normal" @keyup.enter="handleAddActivity('sukan')" @keyup.escape="handleCancelAdd('sukan')" />
            <UiBaseButton type="button" @click="handleAddActivity('sukan')" variant="primary" size="sm"
              :disabled="!newActivityDescriptions.sukan.trim()">
              Simpan
            </UiBaseButton>
            <UiBaseButton type="button" @click="handleCancelAdd('sukan')" variant="outline" size="sm">
              Batal
            </UiBaseButton>
          </div>
        </div>

        <!-- Add Button -->
        <div v-else
          class="p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
          <button @click="handleShowAddForm('sukan')"
            class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors">
            + Tambah Aktiviti / Kegiatan
          </button>
        </div>

        <!-- Activities List -->
        <div v-if="activities.sukan.length > 0" class="space-y-1">
          <div v-for="(activity, index) in activities.sukan" :key="activity.id"
            class="flex items-center justify-between p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded transition-colors">
            <!-- Edit Mode -->
            <div v-if="editingActivityId === activity.id" class="flex items-center space-x-3 flex-1">
              <span class="text-sm font-medium text-gray-500 dark:text-gray-400 min-w-[1.5rem]">
                {{ index + 1 }}.
              </span>
              <Input v-model="editActivityDescription" class="flex-1" @keyup.enter="handleSaveEdit"
                @keyup.escape="handleCancelEdit" ref="editInput" />
              <UiBaseButton type="button" @click="handleSaveEdit" variant="primary" size="sm"
                :disabled="!editActivityDescription.trim()">
                Simpan
              </UiBaseButton>
              <UiBaseButton type="button" @click="handleCancelEdit" variant="outline" size="sm">
                Batal
              </UiBaseButton>
            </div>

            <!-- View Mode -->
            <div v-else class="flex items-center space-x-3 flex-1">
              <span class="text-sm font-medium text-gray-500 dark:text-gray-400 min-w-[1.5rem]">
                {{ index + 1 }}.
              </span>
              <span class="text-sm text-gray-900 dark:text-white flex-1">
                {{ activity.activity_description }}
              </span>
              <div class="flex items-center space-x-2">
                <button @click="handleEditActivity(activity)"
                  class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors">
                  Edit
                </button>
                <span class="text-gray-300 dark:text-gray-600">|</span>
                <button @click="handleDeleteActivity(activity.id)"
                  class="text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 transition-colors">
                  Padam
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Pertubuhan Section -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <!-- Category Header -->
      <div
        class="flex items-center justify-center border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-800">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Pertubuhan</h3>
      </div>

      <!-- Activities List -->
      <div class="space-y-2 col-span-2">
        <!-- Add Activity Form -->
        <div v-if="showAddForms.pertubuhan"
          class="p-3 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800">
          <div class="flex items-center space-x-3">
            <Input v-model="newActivityDescriptions.pertubuhan" placeholder="Taipkan Aktiviti / Kegiatan" class="flex-1"
              variant="normal" @keyup.enter="handleAddActivity('pertubuhan')"
              @keyup.escape="handleCancelAdd('pertubuhan')" />
            <UiBaseButton type="button" @click="handleAddActivity('pertubuhan')" variant="primary" size="sm"
              :disabled="!newActivityDescriptions.pertubuhan.trim()">
              Simpan
            </UiBaseButton>
            <UiBaseButton type="button" @click="handleCancelAdd('pertubuhan')" variant="outline" size="sm">
              Batal
            </UiBaseButton>
          </div>
        </div>

        <!-- Add Button -->
        <div v-else
          class="p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
          <button @click="handleShowAddForm('pertubuhan')"
            class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors">
            + Tambah Aktiviti / Kegiatan
          </button>
        </div>

        <!-- Activities List -->
        <div v-if="activities.pertubuhan.length > 0" class="space-y-1">
          <div v-for="(activity, index) in activities.pertubuhan" :key="activity.id"
            class="flex items-center justify-between p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded transition-colors">
            <!-- Edit Mode -->
            <div v-if="editingActivityId === activity.id" class="flex items-center space-x-3 flex-1">
              <span class="text-sm font-medium text-gray-500 dark:text-gray-400 min-w-[1.5rem]">
                {{ index + 1 }}.
              </span>
              <Input v-model="editActivityDescription" class="flex-1" @keyup.enter="handleSaveEdit"
                @keyup.escape="handleCancelEdit" ref="editInput" />
              <UiBaseButton type="button" @click="handleSaveEdit" variant="primary" size="sm"
                :disabled="!editActivityDescription.trim()">
                Simpan
              </UiBaseButton>
              <UiBaseButton type="button" @click="handleCancelEdit" variant="outline" size="sm">
                Batal
              </UiBaseButton>
            </div>

            <!-- View Mode -->
            <div v-else class="flex items-center space-x-3 flex-1">
              <span class="text-sm font-medium text-gray-500 dark:text-gray-400 min-w-[1.5rem]">
                {{ index + 1 }}.
              </span>
              <span class="text-sm text-gray-900 dark:text-white flex-1">
                {{ activity.activity_description }}
              </span>
              <div class="flex items-center space-x-2">
                <button @click="handleEditActivity(activity)"
                  class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors">
                  Edit
                </button>
                <span class="text-gray-300 dark:text-gray-600">|</span>
                <button @click="handleDeleteActivity(activity.id)"
                  class="text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 transition-colors">
                  Padam
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Sumbangan Section -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <!-- Category Header -->
      <div
        class="flex items-center justify-center border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-800">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Sumbangan</h3>
      </div>

      <!-- Activities List -->
      <div class="space-y-2 col-span-2">
        <!-- Add Activity Form -->
        <div v-if="showAddForms.sumbangan"
          class="p-3 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800">
          <div class="flex items-center space-x-3">
            <Input v-model="newActivityDescriptions.sumbangan" placeholder="Taipkan Aktiviti / Kegiatan" class="flex-1"
              variant="normal" @keyup.enter="handleAddActivity('sumbangan')"
              @keyup.escape="handleCancelAdd('sumbangan')" />
            <UiBaseButton type="button" @click="handleAddActivity('sumbangan')" variant="primary" size="sm"
              :disabled="!newActivityDescriptions.sumbangan.trim()">
              Simpan
            </UiBaseButton>
            <UiBaseButton type="button" @click="handleCancelAdd('sumbangan')" variant="outline" size="sm">
              Batal
            </UiBaseButton>
          </div>
        </div>

        <!-- Add Button -->
        <div v-else
          class="p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
          <button @click="handleShowAddForm('sumbangan')"
            class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors">
            + Tambah Aktiviti / Kegiatan
          </button>
        </div>

        <!-- Activities List -->
        <div v-if="activities.sumbangan.length > 0" class="space-y-1">
          <div v-for="(activity, index) in activities.sumbangan" :key="activity.id"
            class="flex items-center justify-between p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded transition-colors">
            <!-- Edit Mode -->
            <div v-if="editingActivityId === activity.id" class="flex items-center space-x-3 flex-1">
              <span class="text-sm font-medium text-gray-500 dark:text-gray-400 min-w-[1.5rem]">
                {{ index + 1 }}.
              </span>
              <Input v-model="editActivityDescription" class="flex-1" @keyup.enter="handleSaveEdit"
                @keyup.escape="handleCancelEdit" ref="editInput" />
              <UiBaseButton type="button" @click="handleSaveEdit" variant="primary" size="sm"
                :disabled="!editActivityDescription.trim()">
                Simpan
              </UiBaseButton>
              <UiBaseButton type="button" @click="handleCancelEdit" variant="outline" size="sm">
                Batal
              </UiBaseButton>
            </div>

            <!-- View Mode -->
            <div v-else class="flex items-center space-x-3 flex-1">
              <span class="text-sm font-medium text-gray-500 dark:text-gray-400 min-w-[1.5rem]">
                {{ index + 1 }}.
              </span>
              <span class="text-sm text-gray-900 dark:text-white flex-1">
                {{ activity.activity_description }}
              </span>
              <div class="flex items-center space-x-2">
                <button @click="handleEditActivity(activity)"
                  class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors">
                  Edit
                </button>
                <span class="text-gray-300 dark:text-gray-600">|</span>
                <button @click="handleDeleteActivity(activity.id)"
                  class="text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 transition-colors">
                  Padam
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import Input from '~/components/ui/base/Input.vue'
import UiBaseButton from '~/components/ui/base/Button.vue'

// =====================================================
// TYPES
// =====================================================

interface Activity {
  id: string
  activity_description: string
  category: string
  created_at: string
  updated_at: string
}

interface Activities {
  sukan: Activity[]
  pertubuhan: Activity[]
  sumbangan: Activity[]
}

// =====================================================
// PROPS & EMITS
// =====================================================

interface Props {
  activities: Activities
  showAddForms: Record<string, boolean>
}

interface Emits {
  (e: 'add-activity', category: string, description: string): void
  (e: 'edit-activity', activityId: string, description: string): void
  (e: 'delete-activity', activityId: string): void
  (e: 'show-add-form', category: string): void
  (e: 'cancel-add', category: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// =====================================================
// STATE
// =====================================================

const newActivityDescriptions = ref({
  sukan: '',
  pertubuhan: '',
  sumbangan: ''
})

const editingActivityId = ref<string | null>(null)
const editActivityDescription = ref('')
const editInput = ref<InstanceType<typeof Input> | null>(null)

// =====================================================
// METHODS
// =====================================================

const handleAddActivity = (category: string) => {
  const description = newActivityDescriptions.value[category as keyof typeof newActivityDescriptions.value].trim()
  if (!description) return

  emit('add-activity', category, description)
  newActivityDescriptions.value[category as keyof typeof newActivityDescriptions.value] = ''
}

const handleShowAddForm = (category: string) => {
  emit('show-add-form', category)
}

const handleCancelAdd = (category: string) => {
  newActivityDescriptions.value[category as keyof typeof newActivityDescriptions.value] = ''
  emit('cancel-add', category)
}

const handleEditActivity = async (activity: Activity) => {
  editingActivityId.value = activity.id
  editActivityDescription.value = activity.activity_description

  // Focus the edit input after DOM update
  await nextTick()
  if (editInput.value) {
    editInput.value.$el.querySelector('input')?.focus()
  }
}

const handleSaveEdit = () => {
  const description = editActivityDescription.value.trim()
  if (!description || !editingActivityId.value) return

  emit('edit-activity', editingActivityId.value, description)
  handleCancelEdit()
}

const handleCancelEdit = () => {
  editingActivityId.value = null
  editActivityDescription.value = ''
}

const handleDeleteActivity = (activityId: string) => {
  emit('delete-activity', activityId)
}
</script>

<style scoped>
/* Add any component-specific styles here if needed */
</style>
