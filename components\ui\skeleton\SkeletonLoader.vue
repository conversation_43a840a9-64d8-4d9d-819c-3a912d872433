<template>
  <div :class="containerClasses">
    <!-- Skeleton elements based on variant -->
    <template v-if="variant === 'card'">
      <SkeletonCard v-for="n in count" :key="n" :height="height" :show-avatar="showAvatar"
        :show-actions="showActions" />
    </template>

    <template v-else-if="variant === 'table'">
      <SkeletonTable :rows="count" :columns="columns" :show-header="showHeader" />
    </template>

    <template v-else-if="variant === 'list'">
      <SkeletonListItem v-for="n in count" :key="n" :height="height" :show-avatar="showAvatar" :show-meta="showMeta" />
    </template>

    <template v-else-if="variant === 'form'">
      <SkeletonForm :fields="count" :show-buttons="showActions" />
    </template>

    <template v-else-if="variant === 'stats'">
      <SkeletonStats :items="count" />
    </template>

    <template v-else-if="variant === 'reflection'">
      <SkeletonReflection />
    </template>

    <template v-else-if="variant === 'profile'">
      <SkeletonProfileForm />
    </template>

    <template v-else-if="variant === 'document-cards'">
      <div class="space-y-8">
        <SkeletonPageHeader :title-width="titleWidth" :subtitle-width="subtitleWidth" :action-count="actionCount" />
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <SkeletonDocumentCard v-for="card in cardCount" :key="`card-${card}`" />
        </div>
      </div>
    </template>

    <template v-else-if="variant === 'calendar'">
      <SkeletonCalendar />
    </template>

    <template v-else-if="variant === 'schedule'">
      <SkeletonSchedulePage />
    </template>

    <template v-else-if="variant === 'tugas-guru'">
      <SkeletonTugasGuruPage />
    </template>

    <template v-else>
      <!-- Custom skeleton content -->
      <slot>
        <SkeletonBox :height="height" :width="width" :rounded="rounded" />
      </slot>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import SkeletonCard from './SkeletonCard.vue'
import SkeletonTable from './SkeletonTable.vue'
import SkeletonListItem from './SkeletonListItem.vue'
import SkeletonForm from './SkeletonForm.vue'
import SkeletonStats from './SkeletonStats.vue'
import SkeletonReflection from './SkeletonReflection.vue'
import SkeletonProfileForm from './SkeletonProfileForm.vue'
import SkeletonDocumentCard from './SkeletonDocumentCard.vue'
import SkeletonCalendar from './SkeletonCalendar.vue'
import SkeletonSchedulePage from './SkeletonSchedulePage.vue'
import SkeletonTugasGuruPage from './SkeletonTugasGuruPage.vue'
import SkeletonPageHeader from './SkeletonPageHeader.vue'
import SkeletonBox from './SkeletonBox.vue'

type SkeletonVariant = 'card' | 'table' | 'list' | 'form' | 'stats' | 'reflection' | 'profile' | 'document-cards' | 'calendar' | 'schedule' | 'tugas-guru' | 'custom'

interface Props {
  variant?: SkeletonVariant
  count?: number
  height?: string
  width?: string
  columns?: number
  showAvatar?: boolean
  showActions?: boolean
  showHeader?: boolean
  showMeta?: boolean
  rounded?: boolean
  spacing?: 'none' | 'sm' | 'md' | 'lg'
  // For document-cards variant
  cardCount?: number
  titleWidth?: string
  subtitleWidth?: string
  actionCount?: number
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'custom',
  count: 3,
  height: '4rem',
  width: '100%',
  columns: 4,
  showAvatar: false,
  showActions: false,
  showHeader: true,
  showMeta: true,
  rounded: true,
  spacing: 'md',
  cardCount: 6,
  titleWidth: '20rem',
  subtitleWidth: '30rem',
  actionCount: 2
})

const containerClasses = computed(() => [
  'animate-pulse',
  {
    'space-y-1': props.spacing === 'sm',
    'space-y-4': props.spacing === 'md',
    'space-y-6': props.spacing === 'lg',
    'space-y-0': props.spacing === 'none'
  }
])
</script>
