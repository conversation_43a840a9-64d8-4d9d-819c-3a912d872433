<template>
    <div
        class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow duration-200">
        <!-- Header -->
        <div class="flex items-start justify-between mb-4">
            <div class="flex-1">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                    {{ rptStatus.class_name }}
                </h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">
                    {{ rptStatus.subject_name || rptStatus.subject_abbreviation || 'Subjek' }}
                </p>
            </div>

            <!-- Status Badge -->
            <div class="flex-shrink-0">
                <span v-if="rptStatus.has_rpt"
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                    <Icon name="heroicons:check-circle-solid" class="w-3 h-3 mr-1" />
                    Lengkap
                </span>
                <span v-else
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                    <Icon name="heroicons:clock-solid" class="w-3 h-3 mr-1" />
                    Belum Muat Naik
                </span>
            </div>
        </div>

        <!-- File Information (if RPT exists) -->
        <div v-if="rptStatus.has_rpt && rptStatus.rpt_document" class="mb-4">
            <div
                class="border-2 border-dashed border-gray-300 dark:border-gray-600 text-centerflex text-center p-6 bg-gray-50 dark:bg-gray-700 rounded-md">
                <div class="flex-shrink-0">
                    <Icon :name="getFileTypeIcon(rptStatus.rpt_document.file_mime_type)"
                        class="w-12 h-12 text-blue-600 dark:text-blue-400 mx-auto mb-2" />
                </div>
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {{ rptStatus.rpt_document.file_name }}
                    </p>
                    <div class="items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                        <span>{{ formatFileSize(rptStatus.rpt_document.file_size_bytes) }}</span>
                        <span>{{ formatDate(rptStatus.rpt_document.created_at) }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Area (if no RPT) -->
        <div v-else class="mb-4">
            <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-md p-6 text-center">
                <Icon name="heroicons:document-arrow-up" class="w-12 h-12 text-gray-400 mx-auto mb-2" />
                <p class="text-sm text-gray-600 dark:text-gray-400">
                    Belum ada RPT untuk kelas dan subjek ini
                </p>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex items-center justify-between">
            <div class="flex space-x-2">
                <!-- Preview Button (if RPT exists) -->
                <UiBaseButton v-if="rptStatus.has_rpt" variant="outline" size="sm" :disabled="isLoading"
                    prepend-icon="heroicons:eye" @click="handlePreview">
                    Pratonton
                </UiBaseButton>

                <!-- Upload/Replace Button -->
                <UiBaseButton :variant="rptStatus.has_rpt ? 'outline' : 'primary'" size="sm" :disabled="isLoading"
                    :prepend-icon="rptStatus.has_rpt ? 'heroicons:arrow-path' : 'heroicons:arrow-up-tray'"
                    @click="handleUploadClick">
                    {{ rptStatus.has_rpt ? 'Ganti' : 'Muat Naik' }}
                </UiBaseButton>
            </div>

            <!-- Delete Button (if RPT exists) -->
            <UiBaseButton v-if="rptStatus.has_rpt" variant="alert-error" size="sm" :disabled="isLoading"
                @click="handleDelete">
                <Icon name="heroicons:trash" class="w-4 h-4" />
            </UiBaseButton>
        </div>

        <!-- Hidden File Input -->
        <input ref="fileInputRef" type="file" class="hidden" :accept="acceptedFileTypes" @change="handleFileSelect" />
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { RptStatus, RptDocument } from '~/types/rptDocuments';
import { getFileTypeIcon, formatFileSize } from '~/types/rptDocuments';
import UiBaseButton from '~/components/ui/base/Button.vue';
import Icon from '~/components/ui/base/Icon.vue';

interface Props {
    rptStatus: RptStatus;
    isLoading?: boolean;
}

interface Emits {
    (e: 'upload', classId: string, subjectId: string, file: File): void;
    (e: 'replace', rptDocument: RptDocument, file: File): void;
    (e: 'preview', rptDocument: RptDocument): void;
    (e: 'delete', rptDocument: RptDocument): void;
}

const props = withDefaults(defineProps<Props>(), {
    isLoading: false,
});

const emit = defineEmits<Emits>();

// Refs
const fileInputRef = ref<HTMLInputElement | null>(null);

// Computed
const acceptedFileTypes = computed(() => {
    return '.pdf,.docx,.xlsx,.pptx,.doc,.xls,.ppt';
});

// Methods
const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ms-MY', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
    });
};

const handleUploadClick = () => {
    fileInputRef.value?.click();
};

const handleFileSelect = (event: Event) => {
    const target = event.target as HTMLInputElement;
    const file = target.files?.[0];

    if (!file) return;

    if (props.rptStatus.has_rpt && props.rptStatus.rpt_document) {
        emit('replace', props.rptStatus.rpt_document, file);
    } else {
        emit('upload', props.rptStatus.class_id, props.rptStatus.subject_id, file);
    }

    // Reset file input
    target.value = '';
};

const handlePreview = () => {
    if (props.rptStatus.rpt_document) {
        emit('preview', props.rptStatus.rpt_document);
    }
};

const handleDelete = () => {
    if (props.rptStatus.rpt_document) {
        emit('delete', props.rptStatus.rpt_document);
    }
};
</script>

<style scoped>
/* Add any component-specific styles here if needed */
</style>
