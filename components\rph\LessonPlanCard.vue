<template>
  <div class="p-4 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
    <div class="flex flex-col sm:flex-row justify-between sm:items-start gap-4">
      <div class="flex-grow min-w-0">
        <h3 class="text-base font-semibold text-gray-800 dark:text-white truncate" :title="plan.file_name || ''">
          {{ plan.file_name || 'N/A' }}
        </h3>
        <div class="flex flex-col mt-1 gap-y-1 text-sm text-gray-500 dark:text-gray-400">
          <span><strong class="font-medium text-gray-600 dark:text-gray-300">Kelas & Subjek:</strong> {{
            classSubjectsDisplay }}</span>
          <span><strong class="font-medium text-gray-600 dark:text-gray-300">Hari:</strong> {{ sortedDaysDisplay
          }}</span>
          <div class="flex items-center gap-2 mt-2">
            <span class="text-xs font-medium text-gray-600 dark:text-gray-300">Refleksi:</span>
            <span
              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300">
              <Icon name="heroicons:check-circle" class="h-3 w-3 mr-1" />
              Lengkap
            </span>
          </div>
        </div>
      </div>
      <div class="flex flex-shrink-0 items-center gap-1">
        <!-- Reflection Icon -->
        <UiBaseTooltip text="Lihat Refleksi" position="top">
          <button @click="handleEditReflection()"
            class="h-9 w-9 p-0 flex items-center justify-center rounded-md border transition-colors duration-200 text-green-600 hover:text-green-700 border-green-300 hover:border-green-400 hover:bg-green-50 dark:text-green-400 dark:border-green-600 dark:hover:bg-green-900/20">
            <Icon name="heroicons:chat-bubble-left-ellipsis" class="h-5 w-5" />
          </button>
        </UiBaseTooltip>

        <!-- Preview Icon (only for supported file types) -->
        <UiBaseTooltip v-if="isOfficeDocument || isImageFile" text="Pratonton" position="top">
          <button @click="handlePreview"
            class="h-9 w-9 p-0 flex items-center justify-center rounded-md border border-gray-300 text-gray-600 hover:text-gray-700 hover:border-gray-400 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-800 transition-colors duration-200">
            <Icon name="heroicons:eye" class="h-5 w-5" />
          </button>
        </UiBaseTooltip>

        <!-- Download Icon -->
        <UiBaseTooltip text="Muat Turun" position="top">
          <button @click="handleDownload"
            class="h-9 w-9 p-0 flex items-center justify-center rounded-md border border-gray-300 text-gray-600 hover:text-gray-700 hover:border-gray-400 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-800 transition-colors duration-200">
            <Icon name="heroicons:arrow-down-tray" class="h-5 w-5" />
          </button>
        </UiBaseTooltip>

        <!-- Edit Icon -->
        <UiBaseTooltip text="Edit Rancangan Pengajaran" position="top">
          <button @click="emits('edit', plan)"
            class="h-9 w-9 p-0 flex items-center justify-center rounded-md border border-gray-300 text-gray-600 hover:text-gray-700 hover:border-gray-400 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-800 transition-colors duration-200">
            <Icon name="heroicons:pencil" class="h-5 w-5" />
          </button>
        </UiBaseTooltip>

        <!-- Delete Icon -->
        <UiBaseTooltip text="Padam Rancangan Pengajaran" position="top">
          <button @click="emits('delete', plan)"
            class="h-9 w-9 p-0 flex items-center justify-center rounded-md border border-red-300 text-red-500 hover:text-red-700 hover:border-red-400 hover:bg-red-50 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900/20 transition-colors duration-200">
            <Icon name="heroicons:trash" class="h-5 w-5" />
          </button>
        </UiBaseTooltip>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { LessonPlan } from '~/types/lessonPlans';
import type { UserClassSubjectEntry } from '~/schemas/userSchemas';
import { useSubjects } from '@/composables/useSubjects';
import { useLessonPlanReflections } from '@/composables/useLessonPlanReflections';


interface Props {
  plan: LessonPlan;
  userProfileClassSubjects: UserClassSubjectEntry[];
  weekLabel: string;
}

const props = defineProps<Props>();

const emits = defineEmits<{
  (e: 'edit', plan: LessonPlan): void;
  (e: 'delete', plan: LessonPlan): void;
  (e: 'preview-office', plan: LessonPlan): void;
  (e: 'preview-image', plan: LessonPlan): void;
  (e: 'download-plan', plan: LessonPlan): void;
  (e: 'add-reflection', plan: LessonPlan): void;
  (e: 'edit-reflection', plan: LessonPlan): void;
}>();

const { hasReflection } = useLessonPlanReflections();

// Check if this lesson plan has a reflection (always true since reflections are auto-generated)
const hasReflectionForPlan = computed(() => {
  return true; // Always true since reflections are auto-generated
});

// Reflection status text (always "Lengkap")
const reflectionStatusText = computed(() => {
  return 'Lengkap';
});

const handlePreview = () => {
  if (isOfficeDocument.value) {
    emits('preview-office', props.plan);
  } else if (isImageFile.value) {
    emits('preview-image', props.plan);
  }
};

const handleDownload = () => {
  emits('download-plan', props.plan);
};

const handleAddReflection = () => {
  emits('add-reflection', props.plan);
};

const handleEditReflection = () => {
  emits('edit-reflection', props.plan);
};

const { subjects, loading: subjectsLoading } = useSubjects();

const getSubjectNameById = (subjectId: string | number | null): string => {
  if (subjectId === null || subjectId === undefined) return 'Subjek Tidak Sah';
  const idToFind: string = String(subjectId);

  const subject = subjects.value.find(s => s.id === idToFind);
  if (subject) {
    return subject.name;
  }

  // If subject is not found, check if we are in a loading state.
  if (subjectsLoading.value) {
    return `Memuatkan...`; // Return a generic loading text for the subject
  }

  // If not loading and not found, then it's truly missing.
  return `Subjek (ID: ${idToFind}) Tidak Dijumpai`;
};

const classSubjectsDisplay = computed(() => {
  if (subjectsLoading.value && subjects.value.length === 0) return 'Memuatkan senarai subjek...';
  if (!props.plan.class_subject_ids || props.plan.class_subject_ids.length === 0) return 'N/A';

  const displayLabels = props.plan.class_subject_ids.map(compositeId => {
    const parts = compositeId.split('_');
    if (parts.length !== 2) return `Format ID Tidak Sah: ${compositeId}`;
    const classId = parts[0];
    const subjectIdToFind = parts[1];

    // Find the class name from the user's profile. It should be consistent for a given classId.
    const classEntry = props.userProfileClassSubjects.find(cs => cs.class_id === classId);
    const className = classEntry ? classEntry.className : `Kelas (ID: ${classId})`;

    // Find the subject name using the helper function.
    const subjectName = getSubjectNameById(subjectIdToFind);

    return `${className} - ${subjectName}`;
  });

  return displayLabels.join(', ');
});

// Define the desired order and display names for days
const dayOrder: Record<string, number> = {
  'ahad': 1,
  'isnin': 2,
  'selasa': 3,
  'rabu': 4,
  'khamis': 5,
  'jumaat': 6,
  // Add 'sabtu' here if it becomes an option in the future
};

const dayDisplayNames: Record<string, string> = {
  'ahad': 'Ahad',
  'isnin': 'Isnin',
  'selasa': 'Selasa',
  'rabu': 'Rabu',
  'khamis': 'Khamis',
  'jumaat': 'Jumaat',
  // Add 'sabtu' here if it becomes an option in the future
};

const sortedDaysDisplay = computed(() => {
  if (!props.plan.days_selected || props.plan.days_selected.length === 0) {
    return 'N/A';
  }
  return [...props.plan.days_selected]
    .sort((a, b) => (dayOrder[a] || Infinity) - (dayOrder[b] || Infinity))
    .map(dayId => dayDisplayNames[dayId] || dayId) // Fallback to dayId if display name not found
    .join(', ');
});

const checkIsOfficeDocument = (mimeType?: string | null): boolean => {
  if (!mimeType) return false;
  const officeTypes = [
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
    'application/pdf', // .pdf
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' // .xlsx
  ];
  return officeTypes.includes(mimeType);
};

const checkIsImageFile = (mimeType?: string | null): boolean => {
  if (!mimeType) return false;
  const imageTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp'];
  return imageTypes.includes(mimeType.toLowerCase());
};

const isOfficeDocument = computed(() => checkIsOfficeDocument(props.plan.file_mime_type));
const isImageFile = computed(() => checkIsImageFile(props.plan.file_mime_type));

</script>

<style scoped>
.truncate-multiline {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  /* Adjust number of lines */
  line-clamp: 2;
  /* Standard property */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
