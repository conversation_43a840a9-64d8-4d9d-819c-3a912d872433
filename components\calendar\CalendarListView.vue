<template>
    <div class="space-y-6">
        <!-- Events by Date -->
        <div v-if="groupedEvents.length === 0" class="text-center py-12">
            <UiBaseIcon name="heroicons:calendar-days-solid" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Tiada Acara</h3>
            <p class="text-gray-500 dark:text-gray-400">
                Tiada acara dijumpai untuk bulan ini. Klik "Tambah Acara" untuk menambah acara baru.
            </p>
        </div>

        <div v-else class="space-y-3">
            <div v-for="group in groupedEvents" :key="group.date"
                class="bg-white dark:bg-dark-card rounded-lg shadow-sm border border-gray-200 dark:border-dark-border overflow-hidden">
                <!-- Date Header -->
                <div class="bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-dark-border px-4 py-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="font-semibold text-gray-900 dark:text-white text-base">
                                {{ formatDateHeader(group.date) }}
                            </h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                {{ group.events.length }} acara
                            </p>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                            {{ getDayName(group.date) }}
                        </div>
                    </div>
                </div>

                <!-- Events List -->
                <div class="divide-y divide-gray-200 dark:divide-dark-border">
                    <div v-for="event in group.events" :key="event.id"
                        class="hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer transition-colors border-l-4 p-4"
                        :class="`border-l-${getCategoryConfig(event.category).color}`" @click="handleEventClick(event)">
                        <div class="flex items-start space-x-4">
                            <!-- Category Indicator -->
                            <div class="flex-shrink-0 mt-1">
                                <div class="w-4 h-4 rounded-full"
                                    :class="`bg-${getCategoryConfig(event.category).color}`"></div>
                            </div>

                            <!-- Event Content -->
                            <div class="flex-1 min-w-0">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1 min-w-0">
                                        <h4 class="text-lg font-medium text-gray-900 dark:text-white truncate">
                                            {{ event.title }}
                                        </h4>
                                        <p v-if="event.description"
                                            class="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                                            {{ event.description }}
                                        </p>
                                    </div>

                                    <!-- Category Badge -->
                                    <div class="flex-shrink-0 ml-4">
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                            :class="getCategoryConfig(event.category).bgColor + ' ' + getCategoryConfig(event.category).textColor">
                                            <UiBaseIcon :name="getCategoryConfig(event.category).icon"
                                                class="w-3 h-3 mr-1" />
                                            {{ getCategoryConfig(event.category).label }}
                                        </span>
                                    </div>
                                </div>

                                <!-- Event Details -->
                                <div class="flex items-center space-x-6 mt-3 text-sm text-gray-500 dark:text-gray-400">
                                    <!-- Time -->
                                    <div class="flex items-center space-x-1">
                                        <UiBaseIcon name="heroicons:clock-solid" class="w-4 h-4" />
                                        <span>{{ formatEventTime(event) }}</span>
                                    </div>

                                    <!-- Location -->
                                    <div v-if="event.location" class="flex items-center space-x-1">
                                        <UiBaseIcon name="heroicons:map-pin-solid" class="w-4 h-4" />
                                        <span>{{ event.location }}</span>
                                    </div>

                                    <!-- Recurring Indicator -->
                                    <div v-if="event.is_recurring" class="flex items-center space-x-1">
                                        <UiBaseIcon name="heroicons:arrow-path-solid" class="w-4 h-4" />
                                        <span>Berulang</span>
                                    </div>

                                    <!-- Multi-day Indicator -->
                                    <div v-if="event.end_date && event.end_date !== event.start_date"
                                        class="flex items-center space-x-1">
                                        <UiBaseIcon name="heroicons:calendar-days-solid" class="w-4 h-4" />
                                        <span>Beberapa hari</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Button -->
                            <div class="flex-shrink-0">
                                <UiBaseButton variant="flat" size="sm" prepend-icon="heroicons:chevron-right-solid"
                                    @click.stop="handleEventClick(event)">
                                    Lihat
                                </UiBaseButton>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { CalendarEvent } from '~/types/calendar'
import { getCategoryConfig, isEventOnDate, MALAYSIAN_DAYS } from '~/types/calendar'
import { formatDateToLocalString } from '~/utils/dateUtils'

// Props
interface Props {
    events: readonly CalendarEvent[]
    month: number
    year: number
    isAllYearMode?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    isAllYearMode: false
})

// Emits
const emit = defineEmits<{
    'event-click': [event: CalendarEvent]
}>()



// Computed
const groupedEvents = computed(() => {
    if (props.isAllYearMode) {
        // For "Sepanjang Tahun" mode, group events by date across all months
        const eventsByDate = new Map<string, CalendarEvent[]>()

        props.events.forEach(event => {
            const startDate = event.start_date
            const endDate = event.end_date || event.start_date

            // Handle multi-day events
            const start = new Date(startDate)
            const end = new Date(endDate)

            for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
                const dateString = formatDateToLocalString(d)

                if (!eventsByDate.has(dateString)) {
                    eventsByDate.set(dateString, [])
                }
                eventsByDate.get(dateString)!.push(event)
            }
        })

        // Convert to array and sort by date
        return Array.from(eventsByDate.entries())
            .map(([date, events]) => ({
                date,
                events: events.sort((a, b) => {
                    // Sort by time if available, otherwise by title
                    if (a.start_time && b.start_time) {
                        return a.start_time.localeCompare(b.start_time)
                    }
                    if (a.start_time && !b.start_time) return -1
                    if (!a.start_time && b.start_time) return 1
                    return a.title.localeCompare(b.title)
                })
            }))
            .sort((a, b) => a.date.localeCompare(b.date))
    } else {
        // Original logic for single month view
        const eventsByDate = new Map<string, CalendarEvent[]>()

        // Get all days in the current month
        const daysInMonth = new Date(props.year, props.month + 1, 0).getDate()

        for (let day = 1; day <= daysInMonth; day++) {
            const date = new Date(props.year, props.month, day)
            const dateString = formatDateToLocalString(date)

            const dayEvents = props.events.filter(event => isEventOnDate(event, dateString))

            if (dayEvents.length > 0) {
                eventsByDate.set(dateString, dayEvents.sort((a, b) => {
                    // Sort by time if available, otherwise by title
                    if (a.start_time && b.start_time) {
                        return a.start_time.localeCompare(b.start_time)
                    }
                    if (a.start_time && !b.start_time) return -1
                    if (!a.start_time && b.start_time) return 1
                    return a.title.localeCompare(b.title)
                }))
            }
        }

        // Convert to array and sort by date
        return Array.from(eventsByDate.entries())
            .map(([date, events]) => ({ date, events }))
            .sort((a, b) => a.date.localeCompare(b.date))
    }
})



// Methods
const handleEventClick = (event: CalendarEvent) => {
    emit('event-click', event)
}

const formatDateHeader = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('ms-MY', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
    })
}

const getDayName = (dateString: string) => {
    const date = new Date(dateString)
    return MALAYSIAN_DAYS[date.getDay()]
}

const formatEventTime = (event: CalendarEvent) => {
    if (event.is_all_day) {
        return 'Sepanjang hari'
    }

    if (event.start_time) {
        const endTime = event.end_time ? ` - ${event.end_time}` : ''
        return `${event.start_time}${endTime}`
    }

    return 'Tiada masa ditetapkan'
}


</script>

<style scoped>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
