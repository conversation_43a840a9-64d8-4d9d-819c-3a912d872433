<template>
    <!-- Page Loading State -->
    <SkeletonLoader v-if="isPageLoading" variant="document-cards" :card-count="6" :title-width="'20rem'"
        :subtitle-width="'35rem'" />

    <div v-if="!isPageLoading" class="space-y-8">
        <!-- <PERSON> Header -->
        <UiCompositePageHeader title="Pengurusan Rancangan Pengajaran Tahunan (RPT)"
            subtitle="Urus dokumen RPT untuk setiap kelas dan subjek yang anda ajar"
            icon="heroicons:academic-cap-solid">
            <template #actions>
                <div class="text-right">
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {{ uploadedCount }}/{{ totalClassSubjects }}
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                        RPT Lengkap
                    </div>
                </div>
            </template>
        </UiCompositePageHeader>



        <!-- Empty State (No Class Subjects) -->
        <UiCompositeCard v-if="userClassSubjects.length === 0 && !isLoadingClassSubjects">
            <div class="text-center py-12">
                <Icon name="heroicons:academic-cap" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Tiada Kelas & Subjek
                </h3>
                <p class="text-gray-600 dark:text-gray-400 mb-6">
                    Anda perlu menetapkan kelas dan subjek yang anda ajar terlebih dahulu sebelum boleh menguruskan RPT.
                </p>
                <UiBaseButton variant="primary" @click="navigateToProfile">
                    <Icon name="heroicons:cog-6-tooth" class="w-4 h-4 mr-2" />
                    Pergi ke Profil
                </UiBaseButton>
            </div>
        </UiCompositeCard>

        <!-- RPT Cards Grid -->
        <div v-else-if="rptStatusList.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <RptCard v-for="rptStatus in rptStatusList" :key="`${rptStatus.class_id}_${rptStatus.subject_id}`"
                :rpt-status="rptStatus" :is-loading="isLoadingRpt" @upload="handleUpload" @replace="handleReplace"
                @preview="handlePreview" @delete="handleDeleteClick" />
        </div>

        <!-- Loading State -->
        <div v-else-if="isLoadingClassSubjects || isLoadingRpt"
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <SkeletonDocumentCard v-for="card in 6" :key="`card-${card}`" />
        </div>
    </div>

    <!-- Modals -->
    <UiCompositeDeleteConfirmationModal :is-open="showDeleteConfirmationModal" item-type="RPT"
        :item-name="rptToDelete?.file_name || 'dokumen'" :item-subtitle="deleteModalSubtitle"
        warning-message="RPT ini akan dipadam secara kekal. Tindakan ini tidak boleh dibatalkan."
        @confirm="confirmDeleteRpt" @cancel="showDeleteConfirmationModal = false" />

    <FilePreviewModal :is-open="showPreviewModal" :title="previewModalTitle" :preview-url="previewModalUrl"
        :preview-type="previewModalType" :is-loading="isPreviewLoading" :raw-file-url="newTabPreviewUrl"
        @close="showPreviewModal = false" />
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useRptDocuments } from '~/composables/useRptDocuments';
import { useSubjects } from '~/composables/useSubjects';
import type { RptDocument } from '~/types/rptDocuments';
import { getPreviewType } from '~/types/rptDocuments';
import type { UserClassSubjectEntry } from '~/schemas/userSchemas';
import { getClassLevelName } from '~/utils/classLevelMapping';
import UiCompositeCard from '~/components/ui/composite/Card.vue';
import UiCompositePageHeader from '~/components/ui/composite/PageHeader.vue';
import UiBaseButton from '~/components/ui/base/Button.vue';
import { useToast } from '~/composables/useToast';
import UiCompositeDeleteConfirmationModal from '~/components/ui/composite/DeleteConfirmationModal.vue';
import FilePreviewModal from '~/components/rph/FilePreviewModal.vue';
import RptCard from '~/components/rpt/RptCard.vue';
import Icon from '~/components/ui/base/Icon.vue';
import SkeletonLoader from '~/components/ui/skeleton/SkeletonLoader.vue';
import SkeletonDocumentCard from '~/components/ui/skeleton/SkeletonDocumentCard.vue';

// Set page metadata
definePageMeta({
    layout: 'default'
});

// Composables
const router = useRouter();
const supabase = useSupabaseClient();
const user = useSupabaseUser();

const {
    loading: isLoadingRpt,
    error: rptError,
    fetchRptDocuments,
    getRptStatusForClassSubjects,
    uploadRptDocument,
    updateRptDocument,
    deleteRptDocument,
    getTemporaryPublicUrl,
    validateFile,
} = useRptDocuments();

const { subjects, fetchSubjects } = useSubjects();

// State
const isPageLoading = ref(true);
const isLoadingClassSubjects = ref(false);
const userClassSubjects = ref<UserClassSubjectEntry[]>([]);

// Toast notifications
const { success: showSuccessToast, error: showErrorToast } = useToast();

// Modal states
const showDeleteConfirmationModal = ref(false);
const rptToDelete = ref<RptDocument | null>(null);

const showPreviewModal = ref(false);
const previewModalTitle = ref('Pratonton Fail');
const previewModalUrl = ref<string | null>(null);
const previewModalType = ref<'image' | 'office' | null>(null);
const isPreviewLoading = ref(false);
const newTabPreviewUrl = ref<string | null>(null);

// Computed
const rptStatusList = computed(() => {
    const statusList = getRptStatusForClassSubjects(userClassSubjects.value);

    // Enhance with subject names from subjects store
    return statusList.map(status => {
        const subject = subjects.value.find(s => s.id === status.subject_id);
        return {
            ...status,
            subject_name: subject?.name || status.subject_abbreviation || 'Subjek',
        };
    });
});

const totalClassSubjects = computed(() => userClassSubjects.value.length);
const uploadedCount = computed(() => rptStatusList.value.filter(status => status.has_rpt).length);

// Computed property for delete modal subtitle with class level name
const deleteModalSubtitle = computed(() => {
    if (!rptToDelete.value) return '';
    const classLevelName = getClassLevelName(rptToDelete.value.class_id);
    return `${classLevelName} - ${rptToDelete.value.subject_name}`;
});

// Methods
const loadUserClassSubjects = async () => {
    if (!user.value) return;

    isLoadingClassSubjects.value = true;
    try {
        const { data: profile, error: fetchError } = await supabase
            .from('profiles')
            .select('class_subjects')
            .eq('id', user.value.id)
            .single();

        if (fetchError) {
            console.error('Error fetching profile:', fetchError);
            userClassSubjects.value = [];
            return;
        }

        const classSubjects = ((profile as any)?.class_subjects as UserClassSubjectEntry[]) || [];
        userClassSubjects.value = classSubjects;
    } catch (error) {
        console.error('Error loading user class subjects:', error);
        userClassSubjects.value = [];
    } finally {
        isLoadingClassSubjects.value = false;
    }
};

const initializePage = async () => {
    isPageLoading.value = true;

    try {
        // Load data in parallel
        await Promise.all([
            loadUserClassSubjects(),
            fetchSubjects(),
            fetchRptDocuments(),
        ]);
    } catch (error) {
        console.error('Error initializing RPT page:', error);
        showErrorToast('Terdapat masalah semasa memuatkan data. Sila cuba lagi.');
    } finally {
        isPageLoading.value = false;
    }
};

const navigateToProfile = () => {
    router.push('/profile');
};

const handleUpload = async (classId: string, subjectId: string, file: File) => {
    // Validate file
    const validation = validateFile(file);
    if (!validation.isValid) {
        showErrorToast(validation.errorMessage || 'Fail tidak sah');
        return;
    }

    // Find any class subject info with matching class_id and subject_id
    const classSubject = userClassSubjects.value.find(
        cs => cs.class_id === classId && cs.subject_id === subjectId
    );

    if (!classSubject) {
        showErrorToast('Maklumat kelas dan subjek tidak dijumpai');
        return;
    }

    const subject = subjects.value.find(s => s.id === subjectId);
    const subjectName = subject?.name || classSubject.subject_abbreviation || 'Subjek';
    const classLevelName = getClassLevelName(classId);

    const rptInput = {
        class_id: classId,
        subject_id: subjectId,
        class_name: classLevelName, // Use class level name instead of specific class name
        subject_name: subjectName,
    };

    const result = await uploadRptDocument(rptInput, file);

    if (result) {
        showSuccessToast(`RPT untuk ${classLevelName} - ${subjectName} telah berjaya dimuat naik.`);
    } else {
        showErrorToast(rptError.value || 'Gagal memuat naik RPT. Sila cuba lagi.');
    }
};

const handleReplace = async (rptDocument: RptDocument, file: File) => {
    // Validate file
    const validation = validateFile(file);
    if (!validation.isValid) {
        showErrorToast(validation.errorMessage || 'Fail tidak sah');
        return;
    }

    const result = await updateRptDocument(rptDocument.id, file);

    if (result) {
        // Use class level name for display
        const classLevelName = getClassLevelName(rptDocument.class_id);
        showSuccessToast(`RPT untuk ${classLevelName} - ${rptDocument.subject_name} telah berjaya dikemas kini.`);
    } else {
        showErrorToast(rptError.value || 'Gagal mengemas kini RPT. Sila cuba lagi.');
    }
};

const handlePreview = async (rptDocument: RptDocument) => {
    previewModalTitle.value = `Pratonton: ${rptDocument.file_name}`;
    previewModalType.value = getPreviewType(rptDocument.file_mime_type);
    previewModalUrl.value = null;
    newTabPreviewUrl.value = null;
    isPreviewLoading.value = true;
    showPreviewModal.value = true;

    try {
        const signedUrl = await getTemporaryPublicUrl(rptDocument.storage_file_path);

        if (signedUrl) {
            if (previewModalType.value === 'office') {
                if (rptDocument.file_mime_type === 'application/pdf') {
                    // Use Google Docs viewer for PDF files in iframe
                    previewModalUrl.value = `https://docs.google.com/gview?url=${encodeURIComponent(signedUrl)}&embedded=true`;
                    // For new tab, open the PDF directly
                    newTabPreviewUrl.value = signedUrl;
                } else {
                    // Use Microsoft Office Online Viewer for other office documents (docx, xlsx)
                    previewModalUrl.value = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(signedUrl)}`;
                    // For new tab, use the non-embedded viewer which has more controls
                    newTabPreviewUrl.value = `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(signedUrl)}`;
                }
            } else {
                // For images, both URLs are the same
                previewModalUrl.value = signedUrl;
                newTabPreviewUrl.value = signedUrl;
            }
        } else {
            throw new Error('Failed to get file URL');
        }
    } catch (error) {
        console.error('Error setting up preview:', error);
        showErrorToast('Gagal memuatkan pratonton fail. Sila cuba lagi.');
        showPreviewModal.value = false;
    } finally {
        isPreviewLoading.value = false;
    }
};

const handleDeleteClick = (rptDocument: RptDocument) => {
    rptToDelete.value = rptDocument;
    showDeleteConfirmationModal.value = true;
};

const confirmDeleteRpt = async () => {
    if (!rptToDelete.value) return;

    const result = await deleteRptDocument(rptToDelete.value.id);

    if (result) {
        // Use class level name for display
        const classLevelName = getClassLevelName(rptToDelete.value.class_id);
        showSuccessToast(`RPT untuk ${classLevelName} - ${rptToDelete.value.subject_name} telah berjaya dipadam.`);
    } else {
        showErrorToast(rptError.value || 'Gagal memadam RPT. Sila cuba lagi.');
    }

    showDeleteConfirmationModal.value = false;
    rptToDelete.value = null;
};

// Lifecycle
onMounted(() => {
    initializePage();
});

// Watch for authentication changes
watch(user, (newUser) => {
    if (newUser) {
        initializePage();
    }
}, { immediate: true });
</script>
