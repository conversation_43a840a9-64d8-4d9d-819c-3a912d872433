<template>
    <header
        class="bg-white/80 dark:bg-dark-card/80 backdrop-blur-md border-b border-gray-200/50 dark:border-dark-border/50 sticky top-0 z-50">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo and Brand -->
                <div class="flex items-center space-x-4"> <!-- Mobile menu button -->
                    <UiBaseButton @click="toggleMobileSidebar" variant="flat" size="sm" class="lg:hidden"
                        aria-label="Toggle menu">
                        <UiBaseIcon name="heroicons:bars-3-solid" class="w-5 h-5" />
                    </UiBaseButton>

                    <!-- Logo -->
                    <NuxtLink to="/" class="flex items-center space-x-3 group">
                        <div
                            class="w-8 h-8 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform">
                            <Icon name="heroicons:academic-cap-solid" class="w-5 h-5 text-white" />
                        </div>
                        <div class="hidden sm:block">
                            <h1 class="text-xl font-bold text-gray-900 dark:text-white">EduPlan Pro</h1>
                            <p class="text-xs text-gray-500 dark:text-gray-400 -mt-1">Teaching Platform</p>
                        </div>
                    </NuxtLink>
                </div>

                <!-- Right Side Actions -->
                <div class="flex items-center space-x-3">
                    <!-- Notifications -->
                    <div class="relative" data-notifications>
                        <UiBaseButton variant="flat" size="sm" class="relative" @click="toggleNotifications"
                            aria-label="View notifications" :aria-expanded="isNotificationsOpen.toString()"
                            aria-controls="notifications-dropdown">
                            <UiBaseIcon name="heroicons:bell-solid" class="w-5 h-5" />
                            <!-- Notification Badge -->
                            <span v-if="notificationCount > 0"
                                class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                                {{ notificationCount > 9 ? '9+' : notificationCount }}
                            </span>
                        </UiBaseButton>
                        <!-- Notifications Dropdown Menu -->
                        <div v-if="isNotificationsOpen" id="notifications-dropdown"
                            class="absolute right-0 mt-2 w-72 sm:w-80 bg-white dark:bg-dark-card rounded-lg shadow-lg border border-gray-200 dark:border-dark-border py-1 z-50">
                            <div class="px-4 py-3 border-b border-gray-200 dark:border-dark-border">
                                <h3 class="text-sm font-semibold text-gray-900 dark:text-white">Notifications</h3>
                            </div>
                            <div class="max-h-64 overflow-y-auto custom-scrollbar">
                                <div v-if="notificationCount > 0">
                                    <!-- Example Notification Item 1 -->
                                    <div
                                        class="p-3 hover:bg-gray-100 dark:hover:bg-dark-hover cursor-pointer border-b border-gray-100 dark:border-dark-border/50">
                                        <p class="text-sm text-gray-700 dark:text-gray-300 truncate">New feature: Dark
                                            Mode is now live!</p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-0.5">15 minutes ago</p>
                                    </div>
                                    <!-- Example Notification Item 2 -->
                                    <div
                                        class="p-3 hover:bg-gray-100 dark:hover:bg-dark-hover cursor-pointer border-b border-gray-100 dark:border-dark-border/50">
                                        <p class="text-sm text-gray-700 dark:text-gray-300 truncate">Your weekly report
                                            is ready for download.</p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-0.5">2 hours ago</p>
                                    </div>
                                    <!-- Example Notification Item 3 -->
                                    <div class="p-3 hover:bg-gray-100 dark:hover:bg-dark-hover cursor-pointer">
                                        <p class="text-sm text-gray-700 dark:text-gray-300 truncate">Reminder: Project
                                            deadline approaching.</p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-0.5">1 day ago</p>
                                    </div>
                                </div>
                                <div v-else class="p-4 text-center">
                                    <UiBaseIcon name="heroicons:bell-slash"
                                        class="w-8 h-8 text-gray-400 dark:text-gray-500 mx-auto mb-2" />
                                    <p class="text-sm text-gray-500 dark:text-gray-400">No new notifications.</p>
                                </div>
                            </div>
                            <div v-if="notificationCount > 0"
                                class="px-4 py-2 border-t border-gray-200 dark:border-dark-border text-center">
                                <NuxtLink to="/notifications" @click="isNotificationsOpen = false"
                                    class="text-sm font-medium text-primary hover:underline">
                                    View all notifications
                                </NuxtLink>
                            </div>
                        </div>
                    </div>

                    <!-- Theme Switcher -->
                    <ClientOnly>
                        <UiBaseThemeSwitcher />
                    </ClientOnly>
                    <!-- User Menu -->
                    <div v-if="user" class="relative" data-user-menu>
                        <UiBaseButton @click="toggleUserMenu" variant="flat" size="sm"
                            class="flex items-center space-x-2" aria-label="Open user menu"
                            :aria-expanded="isUserMenuOpen.toString()" aria-controls="user-menu-dropdown">

                            <!-- Wrapper for avatar, name, and chevron -->
                            <div class="flex items-center space-x-2">
                                <!-- Avatar/User Icon -->
                                <img v-if="user.user_metadata?.avatar_url || user.user_metadata?.picture"
                                    :src="user.user_metadata?.avatar_url || user.user_metadata?.picture"
                                    alt="User Avatar" class="w-6 h-6 rounded-full object-cover">
                                <div v-else
                                    class="w-6 h-6 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center">
                                    <UiBaseIcon name="heroicons:user-solid" class="w-4 h-4 text-white" />
                                </div>
                                <span class="hidden sm:inline text-sm font-medium">{{ user.user_metadata?.full_name ||
                                    user.email?.split('@')[0] || 'User' }}
                                </span>
                                <UiBaseIcon name="heroicons:chevron-down-solid" class="w-4 h-4" />
                            </div>
                            <!-- End of wrapper -->

                        </UiBaseButton>
                        <!-- User Dropdown Menu -->
                        <div v-if="isUserMenuOpen" id="user-menu-dropdown"
                            class="absolute right-0 mt-2 w-56 bg-white dark:bg-dark-card rounded-lg shadow-lg border border-gray-200 dark:border-dark-border py-2 z-50">
                            <div class="px-4 py-2 border-b border-gray-200 dark:border-dark-border">
                                <p class="text-sm font-medium text-gray-900 dark:text-white truncate">{{
                                    user.user_metadata?.full_name || user.email }}</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Online</p>
                            </div>
                            <NuxtLink to="/profile" @click="isUserMenuOpen = false"
                                class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-hover transition-colors">
                                <UiBaseIcon name="heroicons:user-circle" class="w-4 h-4 mr-3" />
                                Profile Settings
                            </NuxtLink>
                            <NuxtLink to="/preferences" @click="isUserMenuOpen = false"
                                class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-hover transition-colors">
                                <UiBaseIcon name="heroicons:cog-6-tooth-solid" class="w-4 h-4 mr-3" />
                                Preferences
                            </NuxtLink>
                            <hr class="my-1 border-gray-200 dark:border-dark-border/50">
                            <button @click="handleLogout"
                                class="w-full flex items-center px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-500/10 transition-colors">
                                <UiBaseIcon name="heroicons:arrow-left-on-rectangle-solid" class="w-4 h-4 mr-3" />
                                Sign Out
                            </button>
                        </div>
                    </div>

                    <!-- Login Button (when not authenticated) -->
                    <NuxtLink v-else to="/auth/login">
                        <UiBaseButton variant="primary" size="sm">
                            Sign In
                        </UiBaseButton>
                    </NuxtLink>
                </div>
            </div>
        </div>
    </header>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'; // Removed watchEffect

const user = useSupabaseUser(); // This is a reactive ref, no await needed here

const supabaseClient = useSupabaseClient();
const router = useRouter(); // Auto-imported by Nuxt 3
const route = useRoute(); // Auto-imported by Nuxt 3

// User menu state
const isUserMenuOpen = ref(false);

// Notifications
const notificationCount = ref(3); // Mock notification count
const isNotificationsOpen = ref(false);

// Define emits for communication with parent layout
const emit = defineEmits<{
    'toggle-mobile-sidebar': []
}>();

const toggleMobileSidebar = () => {
    emit('toggle-mobile-sidebar');
};

const toggleUserMenu = () => {
    isUserMenuOpen.value = !isUserMenuOpen.value;
    // Close other menus
    isNotificationsOpen.value = false;
};

const toggleNotifications = () => {
    isNotificationsOpen.value = !isNotificationsOpen.value;
    // Close other menus
    isUserMenuOpen.value = false;
};

const handleLogout = async () => {
    if (!supabaseClient) {
        console.error('Supabase client is not available.');
        return;
    }

    // Close user menu
    isUserMenuOpen.value = false;

    const { error } = await supabaseClient.auth.signOut();
    if (error) {
        console.error('Error logging out:', error.message);
    } else {
        // Redirect to the login page after successful logout
        await router.push('/auth/login');
    }
};

// Close menus when clicking outside
const handleClickOutside = (event: MouseEvent) => {
    const target = event.target as Element;

    // Close user menu if clicked outside
    if (isUserMenuOpen.value && !target.closest('[data-user-menu]')) {
        isUserMenuOpen.value = false;
    }

    // Close notifications if clicked outside
    if (isNotificationsOpen.value && !target.closest('[data-notifications]')) {
        isNotificationsOpen.value = false;
    }
};

// Keyboard shortcuts
const handleKeydown = (event: KeyboardEvent) => {
    // Escape to close menus
    if (event.key === 'Escape') {
        if (isUserMenuOpen.value) {
            isUserMenuOpen.value = false;
        }
        if (isNotificationsOpen.value) {
            isNotificationsOpen.value = false;
        }
    }
};

onMounted(() => {
    document.addEventListener('click', handleClickOutside);
    document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside);
    document.removeEventListener('keydown', handleKeydown);
});
</script>
