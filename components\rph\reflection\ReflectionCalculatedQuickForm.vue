<template>
    <div class="space-y-6">
        <!-- Calculated Rating Display -->
        <div
            class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-6 rounded-lg border border-blue-200 dark:border-blue-800">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100">
                    <PERSON><PERSON><PERSON> (Auto-Kira)
                </h3>
                <div class="flex items-center space-x-2">
                    <Icon name="mdi:calculator" class="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    <span class="text-sm text-blue-600 dark:text-blue-400 font-medium">Dikira Automatik</span>
                </div>
            </div>

            <!-- Loading State -->
            <div v-if="calculationLoading" class="flex items-center justify-center py-8">
                <Icon name="mdi:loading" class="h-6 w-6 animate-spin text-blue-500 mr-2" />
                <span class="text-blue-700 dark:text-blue-300">Mengira penilaian...</span>
            </div>

            <!-- No Teacher Schedule State -->
            <div v-else-if="!hasCompatibleSchedules" class="text-center py-6">
                <Icon name="mdi:calendar-remove" class="h-12 w-12 text-orange-400 mx-auto mb-3" />
                <h4 class="text-lg font-medium text-orange-800 dark:text-orange-200 mb-2">
                    Tiada Jadual Mengajar
                </h4>
                <p class="text-sm text-orange-600 dark:text-orange-300 mb-4">
                    Sila tetapkan jadual mengajar untuk mengira penilaian automatik.
                </p>
                <div class="flex flex-col items-center space-y-2">
                    <NuxtLink to="/schedule"
                        class="inline-flex items-center px-4 py-2 bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 rounded-lg hover:bg-orange-200 dark:hover:bg-orange-800 transition-colors">
                        <Icon name="mdi:calendar-plus" class="h-4 w-4 mr-2" />
                        Pergi ke Pengurusan Jadual
                    </NuxtLink>
                </div>
            </div>

            <!-- Calculated Rating Display -->
            <div v-else class="space-y-4">
                <!-- Main Rating -->
                <div class="flex items-center justify-center">
                    <div class="text-center">
                        <div class="flex items-center justify-center space-x-1 mb-2">
                            <Icon v-for="star in 5" :key="star" name="mdi:star"
                                class="h-8 w-8 transition-colors duration-200" :class="star <= Math.round(calculatedRating?.calculated_rating || 0)
                                    ? 'text-yellow-400'
                                    : 'text-gray-300 dark:text-gray-600'" />
                        </div>
                        <div class="text-3xl font-bold text-blue-900 dark:text-blue-100 mb-1">
                            {{ calculatedRating?.calculated_rating.toFixed(1) || '0.0' }}
                        </div>
                        <p class="text-sm text-blue-600 dark:text-blue-400">
                            daripada 5.0 bintang
                        </p>
                    </div>
                </div>

                <!-- Rating Breakdown -->
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 pt-4 border-t border-blue-200 dark:border-blue-700">
                    <div class="text-center">
                        <div class="text-2xl font-semibold text-blue-900 dark:text-blue-100">
                            {{ calculatedRating?.total_periods || 0 }}
                        </div>
                        <p class="text-xs text-blue-600 dark:text-blue-400">Jumlah Waktu</p>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-semibold text-green-600 dark:text-green-400">
                            {{ calculatedRating?.periods_with_reflections || 0 }}
                        </div>
                        <p class="text-xs text-blue-600 dark:text-blue-400">Ada Refleksi</p>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-semibold text-red-500 dark:text-red-400">
                            {{ calculatedRating?.periods_tidak_terlaksana || 0 }}
                        </div>
                        <p class="text-xs text-blue-600 dark:text-blue-400">Tidak Terlaksana</p>
                    </div>
                </div>

                <!-- Detailed Breakdown Toggle -->
                <div class="pt-4">
                    <button @click="showBreakdown = !showBreakdown"
                        class="w-full flex items-center justify-center space-x-2 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 transition-colors">
                        <span>{{ showBreakdown ? 'Sembunyikan' : 'Tunjukkan' }} Butiran</span>
                        <Icon :name="showBreakdown ? 'mdi:chevron-up' : 'mdi:chevron-down'" class="h-4 w-4" />
                    </button>
                </div>

                <!-- Detailed Breakdown -->
                <div v-if="showBreakdown" class="pt-4 border-t border-blue-200 dark:border-blue-700 space-y-2">
                    <h4 class="text-sm font-medium text-blue-900 dark:text-blue-100 mb-3">
                        Butiran Penilaian Mengikut Waktu:
                    </h4>
                    <div class="space-y-2 max-h-40 overflow-y-auto">
                        <div v-for="breakdown in ratingBreakdown"
                            :key="`${breakdown.class_subject_label}-${breakdown.day_label}`"
                            class="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg border border-blue-100 dark:border-blue-800">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-900 dark:text-white">
                                    {{ breakdown.class_subject_label }}
                                </div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                    {{ breakdown.day_label }}
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="flex items-center space-x-1">
                                    <Icon v-for="star in 5" :key="star" name="mdi:star" class="h-3 w-3" :class="star <= breakdown.rating
                                        ? 'text-yellow-400'
                                        : 'text-gray-300 dark:text-gray-600'" />
                                </div>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">
                                    {{ breakdown.rating }}
                                </span>
                                <span class="text-xs px-2 py-1 rounded-full" :class="breakdown.has_reflection
                                    ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                                    : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'">
                                    {{ breakdown.has_reflection ? 'Refleksi' : 'Default' }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Read-only Display Message -->
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div class="flex items-start space-x-2">
                <Icon name="mdi:information" class="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5" />
                <div class="text-sm text-blue-800 dark:text-blue-200">
                    <p class="font-medium mb-1">Refleksi Auto-Kira</p>
                    <p class="text-xs text-blue-700 dark:text-blue-300">
                        Refleksi ini dijana secara automatik berdasarkan data refleksi terperinci.
                    </p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import Icon from '~/components/ui/base/Icon.vue'
import type {
    QuickReflectionFormData,
    CalculatedRating,
    RatingBreakdown
} from '~/types/reflections'
import type { LessonPlan } from '~/types/lessonPlans'
import type { DayOfWeek } from '~/types/teacherSchedule'
import { useRatingCalculation } from '~/composables/useRatingCalculation'
import { useTeacherSchedules } from '~/composables/useTeacherSchedules'

interface Props {
    formData: QuickReflectionFormData & {
        objectives_achieved: boolean
        challenges_faced: string
    }
    errors: Record<string, string[]> | null
    lessonPlan: LessonPlan | null
    loading: boolean
    isEditing: boolean
}

interface Emits {
    (e: 'update:form-data', data: any): void
    (e: 'submit'): void
    (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Composables
const {
    calculateLessonPlanRating,
    createCalculatedRating,
    createRatingBreakdown,
    hasCompatibleSchedules: checkCompatibleSchedules
} = useRatingCalculation()

const { schedules, fetchSchedules } = useTeacherSchedules()

// Local state
const calculationLoading = ref(false)
const calculatedRating = ref<CalculatedRating | null>(null)
const ratingBreakdown = ref<RatingBreakdown[]>([])
const showBreakdown = ref(false)

// Computed properties
const transformSchedulesForRating = (schedulesToTransform: typeof schedules.value) => {
    const result: Array<{
        class_id: string
        subject_id: string
        days_scheduled: DayOfWeek[]
        class_name?: string
        subject_name?: string
    }> = [];

    schedulesToTransform.forEach(schedule => {
        if (schedule.schedule_details?.class_subjects) {
            // New structure: class_subjects array
            schedule.schedule_details.class_subjects.forEach(classSubject => {
                result.push({
                    class_id: classSubject.class_id,
                    subject_id: classSubject.subject_id,
                    days_scheduled: classSubject.days_scheduled,
                    class_name: classSubject.class_name,
                    subject_name: classSubject.subject_name
                });
            });
        } else if (schedule.schedule_details?.periods) {
            // Legacy structure: periods array (for backward compatibility)
            const classSubjectMap = new Map<string, Set<DayOfWeek>>();

            schedule.schedule_details.periods.forEach((period: any) => {
                const key = `${period.class_id}_${period.subject_id}`;
                if (!classSubjectMap.has(key)) {
                    classSubjectMap.set(key, new Set());
                }
                classSubjectMap.get(key)!.add(period.day);
            });

            Array.from(classSubjectMap.entries()).forEach(([key, days]) => {
                const [class_id, subject_id] = key.split('_');
                result.push({
                    class_id,
                    subject_id,
                    days_scheduled: Array.from(days) as DayOfWeek[],
                });
            });
        }
    });

    return result;
};

const hasCompatibleSchedules = computed(() => {
    if (!props.lessonPlan) return false
    const transformed = transformSchedulesForRating(schedules.value);
    return checkCompatibleSchedules(props.lessonPlan, transformed);
})

// Methods (removed updateField since form is now read-only)

const calculateRating = async () => {
    // Prevent multiple simultaneous calculations
    if (calculationLoading.value) {
        return
    }

    if (!props.lessonPlan || !hasCompatibleSchedules.value) {
        calculatedRating.value = null
        ratingBreakdown.value = []
        calculationLoading.value = false
        return
    }

    try {
        calculationLoading.value = true

        // Add timeout to prevent infinite loading
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Calculation timeout')), 10000) // 10 second timeout
        })

        const calculationPromise = (async () => {
            if (!props.lessonPlan) return // Additional safety check

            const transformedSchedules = transformSchedulesForRating(schedules.value);
            const result = await calculateLessonPlanRating(props.lessonPlan, transformedSchedules)
            calculatedRating.value = createCalculatedRating(props.lessonPlan.id, result)
            ratingBreakdown.value = createRatingBreakdown(result)

            // Update parent with calculated rating
            emit('update:form-data', {
                ...props.formData,
                calculated_rating: calculatedRating.value
            })
        })()

        await Promise.race([calculationPromise, timeoutPromise])

    } catch (error) {
        calculatedRating.value = null
        ratingBreakdown.value = []

        // Show error state instead of infinite loading
        if (error instanceof Error && error.message === 'Calculation timeout') {
            // Handle timeout silently
        }
    } finally {
        calculationLoading.value = false
    }
}

// Watchers - only watch for lesson plan changes
watch(() => props.lessonPlan?.id, (newId, oldId) => {
    if (newId && newId !== oldId) {
        calculateRating()
    }
}, { immediate: true })

// Lifecycle
onMounted(async () => {
    try {
        // Fetch teacher schedules first, then calculate rating
        await fetchSchedules()
        await calculateRating()
    } catch (error) {
        calculationLoading.value = false
    }
})

// Expose calculateRating method for parent components
defineExpose({
    calculateRating
})
</script>
