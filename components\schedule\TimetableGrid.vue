<template>
    <div class="overflow-x-auto">
        <div class="min-w-[800px]">
            <!-- Days Header -->
            <div class="grid grid-cols-6 bg-gray-50 dark:bg-gray-700">
                <div
                    class="p-4 text-center font-medium text-gray-700 dark:text-gray-300 border-b border-r border-gray-200 dark:border-gray-600">
                    Waktu
                </div>
                <div v-for="day in days" :key="day.value"
                    class="p-4 text-center font-medium text-gray-700 dark:text-gray-300 border-b border-r border-gray-200 dark:border-gray-600 last:border-r-0">
                    {{ day.label }}
                </div>
            </div>

            <!-- Time Slots -->
            <div v-for="timeSlot in timeSlots" :key="timeSlot.id"
                class="grid grid-cols-6 border-b border-gray-200 dark:border-gray-600 last:border-b-0"
                :class="{ 'bg-yellow-50 dark:bg-yellow-900/10': timeSlot.period_number < 0 }">
                <!-- Time Label -->
                <div
                    class="p-3 border-r border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700/50 relative group">
                    <!-- Time Display -->
                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                        {{ formatTimeLabel(timeSlot) }}
                    </div>
                    <div v-if="timeSlot.period_number >= 0" class="text-xs text-gray-500 dark:text-gray-400">
                        Waktu {{ timeSlot.period_number + 1 }}
                    </div>
                    <!-- Edit Time Button (shown on hover) -->
                    <div v-if="timeSlot.period_number >= 0 && editMode"
                        class="absolute inset-0 bg-blue-50/80 dark:bg-blue-900/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                        <Button variant="outline" size="sm" prepend-icon="mdi:clock-edit"
                            class="bg-white/90 dark:bg-gray-800/90 border-blue-300 dark:border-blue-600 text-blue-600 dark:text-blue-400 shadow-md"
                            @click="$emit('edit-time-slot', timeSlot)">
                            Edit Waktu
                        </Button>
                    </div>
                </div>

                <!-- Day Slots -->
                <div v-for="day in days" :key="`${timeSlot.id}-${day.value}`"
                    class="p-2 border-r border-gray-200 dark:border-gray-600 last:border-r-0 min-h-[80px]"
                    :class="getSlotClasses(timeSlot, day.value)">
                    <div v-if="timeSlot.period_number < 0"
                        class="h-full flex items-center justify-center text-yellow-600 dark:text-yellow-400">
                        <Icon name="mdi:coffee" class="h-5 w-5" />
                    </div>

                    <!-- Class Entry -->
                    <div v-else-if="getTimetableEntry(timeSlot.id, day.value)" class="h-full">
                        <TimetableEntryCard :entry="getTimetableEntry(timeSlot.id, day.value)!"
                            :subject-color="getEntryColor(getTimetableEntry(timeSlot.id, day.value)!)"
                            @edit="$emit('edit-entry', $event)" @delete="$emit('delete-entry', $event)"
                            :editable="editMode" />
                    </div>
                    <!-- Empty Slot -->
                    <div v-else
                        :class="['h-full flex items-center justify-center rounded-md border-2 border-dashed transition-all duration-200', editMode ? 'cursor-pointer border-gray-300 dark:border-gray-600 hover:border-blue-400 dark:hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/10' : 'cursor-not-allowed border-gray-200 dark:border-gray-700 opacity-70']"
                        @click="editMode ? $emit('open-add-modal', timeSlot.id, day.value) : null">
                        <div v-if="editMode" class="flex flex-col items-center justify-center">
                            <Icon name="mdi:plus" class="h-6 w-6 text-gray-400 mb-1" />
                            <span class="text-xs text-gray-500 dark:text-gray-400">Tambah Aktiviti</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add new row at the bottom of the timetable -->
            <div class="p-2">
                <div :class="['h-full flex items-center justify-center rounded-md border-2 border-dashed transition-all duration-200', editMode ? 'cursor-pointer border-gray-300 dark:border-gray-600 hover:border-blue-400 dark:hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/10 p-3' : 'cursor-not-allowed border-gray-200 dark:border-gray-700 opacity-70']"
                    @click="editMode ? $emit('add-time-slot') : null">
                    <div class="flex flex-col items-center justify-center">
                        <Icon name="mdi:plus" class="h-6 w-6 text-gray-400 mb-1" />
                        <span class="text-sm text-gray-500 dark:text-gray-400">Tambah Slot Waktu</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import Button from '~/components/ui/base/Button.vue'
import Icon from '~/components/ui/base/Icon.vue'
import TimetableEntryCard from '~/components/schedule/TimetableEntryCard.vue'
import type { DayOfWeek, TimeSlot, TimetableEntry } from '~/types/timetable'
import { formatTime } from '~/utils/timeHelpers'

defineProps<{
    days: { value: DayOfWeek; label: string }[]
    timeSlots: TimeSlot[]
    timetableEntries: TimetableEntry[]
    editMode: boolean
    getTimetableEntry: (timeSlotId: string, day: DayOfWeek) => TimetableEntry | undefined
    getSlotClasses: (timeSlot: TimeSlot, day: DayOfWeek) => string
    getEntryColor: (entry: TimetableEntry) => any
}>()

defineEmits<{
    'edit-entry': [entry: TimetableEntry]
    'delete-entry': [entry: TimetableEntry]
    'open-add-modal': [timeSlotId: string, day: DayOfWeek]
    'add-time-slot': []
    'edit-time-slot': [timeSlot: TimeSlot]
}>()

const formatTimeLabel = (timeSlot: TimeSlot): string => {
    if (timeSlot.period_number < 0) {
        return timeSlot.label
    }
    return `${formatTime(timeSlot.start_time)} - ${formatTime(timeSlot.end_time)}`
}
</script>