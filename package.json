{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "test": "vitest", "typecheck": "vue-tsc --noEmit"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@iconify/json": "^2.2.348", "@nuxtjs/color-mode": "^3.5.2", "@nuxtjs/supabase": "^1.5.1", "@pinia/nuxt": "^0.11.0", "@tabler/icons-vue": "^3.33.0", "cropperjs": "^2.0.0", "nuxt": "^3.17.4", "pinia": "^3.0.2", "supabase": "^2.23.4", "vue": "^3.5.14", "vue-cropperjs": "^5.0.0", "vue-router": "^4.5.1", "zod": "^3.25.50"}, "devDependencies": {"@iconify-json/heroicons": "^1.2.2", "@iconify-json/mdi": "^1.2.3", "@nuxt/icon": "^1.13.0", "@nuxtjs/google-fonts": "^3.2.0", "@nuxtjs/tailwindcss": "^6.14.0", "@pinia/testing": "^1.0.1", "@tailwindcss/forms": "^0.5.10", "@vitejs/plugin-vue": "^5.2.4", "@vue/test-utils": "^2.4.6", "jsdom": "^26.1.0", "vitest": "^3.1.4", "vue-tsc": "^2.2.10"}}